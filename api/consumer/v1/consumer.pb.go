// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: consumer/v1/consumer.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Empty struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Empty) Reset() {
	*x = Empty{}
	mi := &file_consumer_v1_consumer_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_consumer_v1_consumer_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_consumer_v1_consumer_proto_rawDescGZIP(), []int{0}
}

var File_consumer_v1_consumer_proto protoreflect.FileDescriptor

const file_consumer_v1_consumer_proto_rawDesc = "" +
	"\n" +
	"\x1aconsumer/v1/consumer.proto\x12\vconsumer.v1\x1a\x1cgoogle/api/annotations.proto\"\a\n" +
	"\x05Empty2M\n" +
	"\bConsumer\x12A\n" +
	"\x06Health\x12\x12.consumer.v1.Empty\x1a\x12.consumer.v1.Empty\"\x0f\x82\xd3\xe4\x93\x02\t\x12\a/healthBL\n" +
	"\x1adev.kratos.api.consumer.v1B\x0fConsumerProtoV1P\x01Z\x1bsh_proxy/api/consumer/v1;v1b\x06proto3"

var (
	file_consumer_v1_consumer_proto_rawDescOnce sync.Once
	file_consumer_v1_consumer_proto_rawDescData []byte
)

func file_consumer_v1_consumer_proto_rawDescGZIP() []byte {
	file_consumer_v1_consumer_proto_rawDescOnce.Do(func() {
		file_consumer_v1_consumer_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_consumer_v1_consumer_proto_rawDesc), len(file_consumer_v1_consumer_proto_rawDesc)))
	})
	return file_consumer_v1_consumer_proto_rawDescData
}

var file_consumer_v1_consumer_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_consumer_v1_consumer_proto_goTypes = []any{
	(*Empty)(nil), // 0: consumer.v1.Empty
}
var file_consumer_v1_consumer_proto_depIdxs = []int32{
	0, // 0: consumer.v1.Consumer.Health:input_type -> consumer.v1.Empty
	0, // 1: consumer.v1.Consumer.Health:output_type -> consumer.v1.Empty
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_consumer_v1_consumer_proto_init() }
func file_consumer_v1_consumer_proto_init() {
	if File_consumer_v1_consumer_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_consumer_v1_consumer_proto_rawDesc), len(file_consumer_v1_consumer_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_consumer_v1_consumer_proto_goTypes,
		DependencyIndexes: file_consumer_v1_consumer_proto_depIdxs,
		MessageInfos:      file_consumer_v1_consumer_proto_msgTypes,
	}.Build()
	File_consumer_v1_consumer_proto = out.File
	file_consumer_v1_consumer_proto_goTypes = nil
	file_consumer_v1_consumer_proto_depIdxs = nil
}
