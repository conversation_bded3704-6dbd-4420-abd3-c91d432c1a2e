syntax = "proto3";

package consumer.v1;

import "google/api/annotations.proto";

option go_package = "sh_proxy/api/consumer/v1;v1";
option java_multiple_files = true;
option java_package = "dev.kratos.api.consumer.v1";
option java_outer_classname = "ConsumerProtoV1";

service Consumer {
  // Sends a greeting
  rpc Health (Empty) returns (Empty) {
    option (google.api.http) = {
      get: "/health"
    };
  }
}

message Empty{}
