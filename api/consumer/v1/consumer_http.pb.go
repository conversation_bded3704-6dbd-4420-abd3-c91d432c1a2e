// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.21.12
// source: consumer/v1/consumer.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationConsumerHealth = "/consumer.v1.Consumer/Health"

type ConsumerHTTPServer interface {
	// Health Sends a greeting
	Health(context.Context, *Empty) (*Empty, error)
}

func RegisterConsumerHTTPServer(s *http.Server, srv ConsumerHTTPServer) {
	r := s.Route("/")
	r.GET("/health", _Consumer_Health0_HTTP_Handler(srv))
}

func _Consumer_Health0_HTTP_Handler(srv ConsumerHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in Empty
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationConsumerHealth)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Health(ctx, req.(*Empty))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Empty)
		return ctx.Result(200, reply)
	}
}

type ConsumerHTTPClient interface {
	Health(ctx context.Context, req *Empty, opts ...http.CallOption) (rsp *Empty, err error)
}

type ConsumerHTTPClientImpl struct {
	cc *http.Client
}

func NewConsumerHTTPClient(client *http.Client) ConsumerHTTPClient {
	return &ConsumerHTTPClientImpl{client}
}

func (c *ConsumerHTTPClientImpl) Health(ctx context.Context, in *Empty, opts ...http.CallOption) (*Empty, error) {
	var out Empty
	pattern := "/health"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationConsumerHealth))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
