// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: user/v1/user.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RouteByIpRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Supplier      string                 `protobuf:"bytes,1,opt,name=supplier,proto3" json:"supplier,omitempty"` // 必须有
	Ip            string                 `protobuf:"bytes,2,opt,name=ip,proto3" json:"ip,omitempty"`             // 必须有
	SkdId         string                 `protobuf:"bytes,3,opt,name=skdId,proto3" json:"skdId,omitempty"`       // 可选.
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RouteByIpRequest) Reset() {
	*x = RouteByIpRequest{}
	mi := &file_user_v1_user_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RouteByIpRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RouteByIpRequest) ProtoMessage() {}

func (x *RouteByIpRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RouteByIpRequest.ProtoReflect.Descriptor instead.
func (*RouteByIpRequest) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{0}
}

func (x *RouteByIpRequest) GetSupplier() string {
	if x != nil {
		return x.Supplier
	}
	return ""
}

func (x *RouteByIpRequest) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *RouteByIpRequest) GetSkdId() string {
	if x != nil {
		return x.SkdId
	}
	return ""
}

type RouteByIpReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*IpList              `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RouteByIpReply) Reset() {
	*x = RouteByIpReply{}
	mi := &file_user_v1_user_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RouteByIpReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RouteByIpReply) ProtoMessage() {}

func (x *RouteByIpReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RouteByIpReply.ProtoReflect.Descriptor instead.
func (*RouteByIpReply) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{1}
}

func (x *RouteByIpReply) GetList() []*IpList {
	if x != nil {
		return x.List
	}
	return nil
}

type IpList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Host          string                 `protobuf:"bytes,1,opt,name=host,proto3" json:"host,omitempty"`
	SkdId         string                 `protobuf:"bytes,2,opt,name=skdId,proto3" json:"skdId,omitempty"`
	SdkIp         string                 `protobuf:"bytes,3,opt,name=sdkIp,proto3" json:"sdkIp,omitempty"`
	ConnId        string                 `protobuf:"bytes,4,opt,name=connId,proto3" json:"connId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IpList) Reset() {
	*x = IpList{}
	mi := &file_user_v1_user_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IpList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IpList) ProtoMessage() {}

func (x *IpList) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IpList.ProtoReflect.Descriptor instead.
func (*IpList) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{2}
}

func (x *IpList) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *IpList) GetSkdId() string {
	if x != nil {
		return x.SkdId
	}
	return ""
}

func (x *IpList) GetSdkIp() string {
	if x != nil {
		return x.SdkIp
	}
	return ""
}

func (x *IpList) GetConnId() string {
	if x != nil {
		return x.ConnId
	}
	return ""
}

type RouteRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Location      string                 `protobuf:"bytes,1,opt,name=location,proto3" json:"location,omitempty"` // format US_Florida_Miami,US_Florida,US. empty string 为混播模式.
	AuthUser      string                 `protobuf:"bytes,2,opt,name=authUser,proto3" json:"authUser,omitempty"` // fjwHTv29Mo
	Session       string                 `protobuf:"bytes,3,opt,name=session,proto3" json:"session,omitempty"`   // session id. empty string 代表为轮转模式.
	Host          string                 `protobuf:"bytes,4,opt,name=host,proto3" json:"host,omitempty"`         // 业务host 比如 google.com
	Port          int64                  `protobuf:"varint,5,opt,name=port,proto3" json:"port,omitempty"`        // 业务端口 比如 443
	Supplier      string                 `protobuf:"bytes,6,opt,name=supplier,proto3" json:"supplier,omitempty"` // 供应商
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RouteRequest) Reset() {
	*x = RouteRequest{}
	mi := &file_user_v1_user_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RouteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RouteRequest) ProtoMessage() {}

func (x *RouteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RouteRequest.ProtoReflect.Descriptor instead.
func (*RouteRequest) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{3}
}

func (x *RouteRequest) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *RouteRequest) GetAuthUser() string {
	if x != nil {
		return x.AuthUser
	}
	return ""
}

func (x *RouteRequest) GetSession() string {
	if x != nil {
		return x.Session
	}
	return ""
}

func (x *RouteRequest) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *RouteRequest) GetPort() int64 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *RouteRequest) GetSupplier() string {
	if x != nil {
		return x.Supplier
	}
	return ""
}

type RouteReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Host          string                 `protobuf:"bytes,1,opt,name=host,proto3" json:"host,omitempty"` // 中控地址
	SkdId         string                 `protobuf:"bytes,2,opt,name=skdId,proto3" json:"skdId,omitempty"`
	SdkIp         string                 `protobuf:"bytes,3,opt,name=sdkIp,proto3" json:"sdkIp,omitempty"`
	ConnId        string                 `protobuf:"bytes,4,opt,name=connId,proto3" json:"connId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RouteReply) Reset() {
	*x = RouteReply{}
	mi := &file_user_v1_user_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RouteReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RouteReply) ProtoMessage() {}

func (x *RouteReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RouteReply.ProtoReflect.Descriptor instead.
func (*RouteReply) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{4}
}

func (x *RouteReply) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *RouteReply) GetSkdId() string {
	if x != nil {
		return x.SkdId
	}
	return ""
}

func (x *RouteReply) GetSdkIp() string {
	if x != nil {
		return x.SdkIp
	}
	return ""
}

func (x *RouteReply) GetConnId() string {
	if x != nil {
		return x.ConnId
	}
	return ""
}

type EventRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`         // sdkConnect,sdkDisconnect
	SdkId         string                 `protobuf:"bytes,2,opt,name=sdkId,proto3" json:"sdkId,omitempty"`       // sdk id
	SdkIp         string                 `protobuf:"bytes,3,opt,name=sdkIp,proto3" json:"sdkIp,omitempty"`       // sdk ip
	Host          string                 `protobuf:"bytes,4,opt,name=host,proto3" json:"host,omitempty"`         // 中控pod地址
	Supplier      string                 `protobuf:"bytes,5,opt,name=supplier,proto3" json:"supplier,omitempty"` // 供应商
	Meta          string                 `protobuf:"bytes,6,opt,name=meta,proto3" json:"meta,omitempty"`         // sdk附带信息
	ConnId        string                 `protobuf:"bytes,7,opt,name=connId,proto3" json:"connId,omitempty"`     // 连接id
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EventRequest) Reset() {
	*x = EventRequest{}
	mi := &file_user_v1_user_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EventRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EventRequest) ProtoMessage() {}

func (x *EventRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EventRequest.ProtoReflect.Descriptor instead.
func (*EventRequest) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{5}
}

func (x *EventRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *EventRequest) GetSdkId() string {
	if x != nil {
		return x.SdkId
	}
	return ""
}

func (x *EventRequest) GetSdkIp() string {
	if x != nil {
		return x.SdkIp
	}
	return ""
}

func (x *EventRequest) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *EventRequest) GetSupplier() string {
	if x != nil {
		return x.Supplier
	}
	return ""
}

func (x *EventRequest) GetMeta() string {
	if x != nil {
		return x.Meta
	}
	return ""
}

func (x *EventRequest) GetConnId() string {
	if x != nil {
		return x.ConnId
	}
	return ""
}

type EventReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Message       string                 `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EventReply) Reset() {
	*x = EventReply{}
	mi := &file_user_v1_user_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EventReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EventReply) ProtoMessage() {}

func (x *EventReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EventReply.ProtoReflect.Descriptor instead.
func (*EventReply) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{6}
}

func (x *EventReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// The request message containing the user's name.
type HelloRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HelloRequest) Reset() {
	*x = HelloRequest{}
	mi := &file_user_v1_user_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HelloRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HelloRequest) ProtoMessage() {}

func (x *HelloRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HelloRequest.ProtoReflect.Descriptor instead.
func (*HelloRequest) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{7}
}

func (x *HelloRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// The response message containing the greetings
type HelloReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Message       string                 `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HelloReply) Reset() {
	*x = HelloReply{}
	mi := &file_user_v1_user_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HelloReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HelloReply) ProtoMessage() {}

func (x *HelloReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HelloReply.ProtoReflect.Descriptor instead.
func (*HelloReply) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{8}
}

func (x *HelloReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type Empty struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Empty) Reset() {
	*x = Empty{}
	mi := &file_user_v1_user_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{9}
}

var File_user_v1_user_proto protoreflect.FileDescriptor

const file_user_v1_user_proto_rawDesc = "" +
	"\n" +
	"\x12user/v1/user.proto\x12\auser.v1\x1a\x1cgoogle/api/annotations.proto\"T\n" +
	"\x10RouteByIpRequest\x12\x1a\n" +
	"\bsupplier\x18\x01 \x01(\tR\bsupplier\x12\x0e\n" +
	"\x02ip\x18\x02 \x01(\tR\x02ip\x12\x14\n" +
	"\x05skdId\x18\x03 \x01(\tR\x05skdId\"5\n" +
	"\x0eRouteByIpReply\x12#\n" +
	"\x04list\x18\x01 \x03(\v2\x0f.user.v1.IpListR\x04list\"`\n" +
	"\x06IpList\x12\x12\n" +
	"\x04host\x18\x01 \x01(\tR\x04host\x12\x14\n" +
	"\x05skdId\x18\x02 \x01(\tR\x05skdId\x12\x14\n" +
	"\x05sdkIp\x18\x03 \x01(\tR\x05sdkIp\x12\x16\n" +
	"\x06connId\x18\x04 \x01(\tR\x06connId\"\xa4\x01\n" +
	"\fRouteRequest\x12\x1a\n" +
	"\blocation\x18\x01 \x01(\tR\blocation\x12\x1a\n" +
	"\bauthUser\x18\x02 \x01(\tR\bauthUser\x12\x18\n" +
	"\asession\x18\x03 \x01(\tR\asession\x12\x12\n" +
	"\x04host\x18\x04 \x01(\tR\x04host\x12\x12\n" +
	"\x04port\x18\x05 \x01(\x03R\x04port\x12\x1a\n" +
	"\bsupplier\x18\x06 \x01(\tR\bsupplier\"d\n" +
	"\n" +
	"RouteReply\x12\x12\n" +
	"\x04host\x18\x01 \x01(\tR\x04host\x12\x14\n" +
	"\x05skdId\x18\x02 \x01(\tR\x05skdId\x12\x14\n" +
	"\x05sdkIp\x18\x03 \x01(\tR\x05sdkIp\x12\x16\n" +
	"\x06connId\x18\x04 \x01(\tR\x06connId\"\xaa\x01\n" +
	"\fEventRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x14\n" +
	"\x05sdkId\x18\x02 \x01(\tR\x05sdkId\x12\x14\n" +
	"\x05sdkIp\x18\x03 \x01(\tR\x05sdkIp\x12\x12\n" +
	"\x04host\x18\x04 \x01(\tR\x04host\x12\x1a\n" +
	"\bsupplier\x18\x05 \x01(\tR\bsupplier\x12\x12\n" +
	"\x04meta\x18\x06 \x01(\tR\x04meta\x12\x16\n" +
	"\x06connId\x18\a \x01(\tR\x06connId\"&\n" +
	"\n" +
	"EventReply\x12\x18\n" +
	"\amessage\x18\x01 \x01(\tR\amessage\"\"\n" +
	"\fHelloRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\"&\n" +
	"\n" +
	"HelloReply\x12\x18\n" +
	"\amessage\x18\x01 \x01(\tR\amessage\"\a\n" +
	"\x05Empty2\xb4\x02\n" +
	"\x04User\x129\n" +
	"\x06Health\x12\x0e.user.v1.Empty\x1a\x0e.user.v1.Empty\"\x0f\x82\xd3\xe4\x93\x02\t\x12\a/health\x12I\n" +
	"\x05Event\x12\x15.user.v1.EventRequest\x1a\x13.user.v1.EventReply\"\x14\x82\xd3\xe4\x93\x02\x0e:\x01*\"\t/v1/event\x12I\n" +
	"\x05Route\x12\x15.user.v1.RouteRequest\x1a\x13.user.v1.RouteReply\"\x14\x82\xd3\xe4\x93\x02\x0e:\x01*\"\t/v1/route\x12[\n" +
	"\tRouteByIp\x12\x19.user.v1.RouteByIpRequest\x1a\x17.user.v1.RouteByIpReply\"\x1a\x82\xd3\xe4\x93\x02\x14:\x01*\"\x0f/v1/route-by-ipB@\n" +
	"\x16dev.kratos.api.user.v1B\vUserProtoV1P\x01Z\x17sh_proxy/api/user/v1;v1b\x06proto3"

var (
	file_user_v1_user_proto_rawDescOnce sync.Once
	file_user_v1_user_proto_rawDescData []byte
)

func file_user_v1_user_proto_rawDescGZIP() []byte {
	file_user_v1_user_proto_rawDescOnce.Do(func() {
		file_user_v1_user_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_user_v1_user_proto_rawDesc), len(file_user_v1_user_proto_rawDesc)))
	})
	return file_user_v1_user_proto_rawDescData
}

var file_user_v1_user_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_user_v1_user_proto_goTypes = []any{
	(*RouteByIpRequest)(nil), // 0: user.v1.RouteByIpRequest
	(*RouteByIpReply)(nil),   // 1: user.v1.RouteByIpReply
	(*IpList)(nil),           // 2: user.v1.IpList
	(*RouteRequest)(nil),     // 3: user.v1.RouteRequest
	(*RouteReply)(nil),       // 4: user.v1.RouteReply
	(*EventRequest)(nil),     // 5: user.v1.EventRequest
	(*EventReply)(nil),       // 6: user.v1.EventReply
	(*HelloRequest)(nil),     // 7: user.v1.HelloRequest
	(*HelloReply)(nil),       // 8: user.v1.HelloReply
	(*Empty)(nil),            // 9: user.v1.Empty
}
var file_user_v1_user_proto_depIdxs = []int32{
	2, // 0: user.v1.RouteByIpReply.list:type_name -> user.v1.IpList
	9, // 1: user.v1.User.Health:input_type -> user.v1.Empty
	5, // 2: user.v1.User.Event:input_type -> user.v1.EventRequest
	3, // 3: user.v1.User.Route:input_type -> user.v1.RouteRequest
	0, // 4: user.v1.User.RouteByIp:input_type -> user.v1.RouteByIpRequest
	9, // 5: user.v1.User.Health:output_type -> user.v1.Empty
	6, // 6: user.v1.User.Event:output_type -> user.v1.EventReply
	4, // 7: user.v1.User.Route:output_type -> user.v1.RouteReply
	1, // 8: user.v1.User.RouteByIp:output_type -> user.v1.RouteByIpReply
	5, // [5:9] is the sub-list for method output_type
	1, // [1:5] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_user_v1_user_proto_init() }
func file_user_v1_user_proto_init() {
	if File_user_v1_user_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_user_v1_user_proto_rawDesc), len(file_user_v1_user_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_user_v1_user_proto_goTypes,
		DependencyIndexes: file_user_v1_user_proto_depIdxs,
		MessageInfos:      file_user_v1_user_proto_msgTypes,
	}.Build()
	File_user_v1_user_proto = out.File
	file_user_v1_user_proto_goTypes = nil
	file_user_v1_user_proto_depIdxs = nil
}
