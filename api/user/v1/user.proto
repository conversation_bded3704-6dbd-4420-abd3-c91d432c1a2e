syntax = "proto3";

package user.v1;

import "google/api/annotations.proto";

option go_package = "sh_proxy/api/user/v1;v1";
option java_multiple_files = true;
option java_package = "dev.kratos.api.user.v1";
option java_outer_classname = "UserProtoV1";

// The greeting service definition.
service User {
  // Sends a greeting
  rpc Health (Empty) returns (Empty) {
    option (google.api.http) = {
      get: "/health"
    };
  }
  rpc Event (EventRequest) returns (EventReply) {
    option (google.api.http) = {
      post: "/v1/event"
      body: "*"
    };
  }
  rpc Route(RouteRequest) returns(RouteReply){
    option (google.api.http) = {
      post: "/v1/route"
      body: "*"
    };
  }
  rpc RouteByIp(RouteByIpRequest) returns(RouteByIpReply){
    option (google.api.http) = {
      post: "/v1/route-by-ip"
      body: "*"
    };
  }
}
message RouteByIpRequest{
  string supplier = 1;// 必须有
  string ip = 2; // 必须有
  string  skdId = 3;// 可选.
}
message RouteByIpReply{
  repeated IpList list = 1;
}
message IpList {
  string host = 1;
  string skdId = 2;
  string sdkIp = 3;
  string connId=4;
}
message RouteRequest{
  string location=1;// format US_Florida_Miami,US_Florida,US. empty string 为混播模式.
  string authUser=2;// fjwHTv29Mo
  string session=3; // session id. empty string 代表为轮转模式.
  string host=4;// 业务host 比如 google.com
  int64 port=5;// 业务端口 比如 443
  string supplier=6; // 供应商
}
message RouteReply{
  string host=1; // 中控地址
  string skdId=2;
  string sdkIp=3;
  string connId=4;
}
message EventRequest{
  string name =1; // sdkConnect,sdkDisconnect
  string sdkId=2;// sdk id
  string sdkIp=3;// sdk ip
  string host=4;// 中控pod地址
  string supplier=5; // 供应商
  string meta=6; // sdk附带信息
  string connId=7; // 连接id
}
message EventReply{
  string message = 1;
}
// The request message containing the user's name.
message HelloRequest {
  string name = 1;
}

// The response message containing the greetings
message HelloReply {
  string message = 1;
}

message Empty{}
