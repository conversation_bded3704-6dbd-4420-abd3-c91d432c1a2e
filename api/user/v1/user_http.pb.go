// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.8.4
// - protoc             v3.21.12
// source: user/v1/user.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationUserEvent = "/user.v1.User/Event"
const OperationUserHealth = "/user.v1.User/Health"
const OperationUserRoute = "/user.v1.User/Route"
const OperationUserRouteByIp = "/user.v1.User/RouteByIp"

type UserHTTPServer interface {
	Event(context.Context, *EventRequest) (*EventReply, error)
	// Health Sends a greeting
	Health(context.Context, *Empty) (*Empty, error)
	Route(context.Context, *RouteRequest) (*RouteReply, error)
	RouteByIp(context.Context, *RouteByIpRequest) (*RouteByIpReply, error)
}

func RegisterUserHTTPServer(s *http.Server, srv UserHTTPServer) {
	r := s.Route("/")
	r.GET("/health", _User_Health1_HTTP_Handler(srv))
	r.POST("/v1/event", _User_Event0_HTTP_Handler(srv))
	r.POST("/v1/route", _User_Route0_HTTP_Handler(srv))
	r.POST("/v1/route-by-ip", _User_RouteByIp0_HTTP_Handler(srv))
}

func _User_Health1_HTTP_Handler(srv UserHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in Empty
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserHealth)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Health(ctx, req.(*Empty))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*Empty)
		return ctx.Result(200, reply)
	}
}

func _User_Event0_HTTP_Handler(srv UserHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in EventRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserEvent)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Event(ctx, req.(*EventRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*EventReply)
		return ctx.Result(200, reply)
	}
}

func _User_Route0_HTTP_Handler(srv UserHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RouteRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserRoute)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.Route(ctx, req.(*RouteRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*RouteReply)
		return ctx.Result(200, reply)
	}
}

func _User_RouteByIp0_HTTP_Handler(srv UserHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in RouteByIpRequest
		if err := ctx.Bind(&in); err != nil {
			return err
		}
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationUserRouteByIp)
		h := ctx.Middleware(func(ctx context.Context, req interface{}) (interface{}, error) {
			return srv.RouteByIp(ctx, req.(*RouteByIpRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*RouteByIpReply)
		return ctx.Result(200, reply)
	}
}

type UserHTTPClient interface {
	Event(ctx context.Context, req *EventRequest, opts ...http.CallOption) (rsp *EventReply, err error)
	Health(ctx context.Context, req *Empty, opts ...http.CallOption) (rsp *Empty, err error)
	Route(ctx context.Context, req *RouteRequest, opts ...http.CallOption) (rsp *RouteReply, err error)
	RouteByIp(ctx context.Context, req *RouteByIpRequest, opts ...http.CallOption) (rsp *RouteByIpReply, err error)
}

type UserHTTPClientImpl struct {
	cc *http.Client
}

func NewUserHTTPClient(client *http.Client) UserHTTPClient {
	return &UserHTTPClientImpl{client}
}

func (c *UserHTTPClientImpl) Event(ctx context.Context, in *EventRequest, opts ...http.CallOption) (*EventReply, error) {
	var out EventReply
	pattern := "/v1/event"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserEvent))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserHTTPClientImpl) Health(ctx context.Context, in *Empty, opts ...http.CallOption) (*Empty, error) {
	var out Empty
	pattern := "/health"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationUserHealth))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserHTTPClientImpl) Route(ctx context.Context, in *RouteRequest, opts ...http.CallOption) (*RouteReply, error) {
	var out RouteReply
	pattern := "/v1/route"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserRoute))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}

func (c *UserHTTPClientImpl) RouteByIp(ctx context.Context, in *RouteByIpRequest, opts ...http.CallOption) (*RouteByIpReply, error) {
	var out RouteByIpReply
	pattern := "/v1/route-by-ip"
	path := binding.EncodeURL(pattern, in, false)
	opts = append(opts, http.Operation(OperationUserRouteByIp))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "POST", path, in, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, nil
}
