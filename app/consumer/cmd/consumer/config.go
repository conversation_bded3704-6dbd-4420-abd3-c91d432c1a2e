package main

import (
	"fmt"
	"os"
	"strings"

	"sh_proxy/app/consumer/internal/conf"

	"github.com/go-kratos/kratos/v2/config"
	"github.com/go-kratos/kratos/v2/log"

	// file
	fileKratos "github.com/go-kratos/kratos/v2/config/file"

	// nacos
	nacosKratos "github.com/go-kratos/kratos/contrib/config/nacos/v2"
	nacosClients "github.com/nacos-group/nacos-sdk-go/clients"
	nacosConstant "github.com/nacos-group/nacos-sdk-go/common/constant"
	nacosVo "github.com/nacos-group/nacos-sdk-go/vo"
)

const remoteConfigSourceConfigFile = "remote.yaml"

// NewConfigProvider 创建一个配置
func NewConfigProvider(configPath string) config.Config {
	err, rc := LoadRemoteConfigSourceConfigs(configPath)
	if err != nil {
		log.Error("LoadRemoteConfigSourceConfigs: ", err.Error())
	}
	if rc != nil {
		return config.New(
			config.WithSource(
				NewFileConfigSource(configPath),
				NewRemoteConfigSource(rc),
			),
		)
	} else {
		return config.New(
			config.WithSource(
				NewFileConfigSource(configPath),
			),
		)
	}
}

// LoadBootstrapConfig 加载程序引导配置
func LoadBootstrapConfig(configPath string) *conf.Bootstrap {
	cfg := NewConfigProvider(configPath)
	if err := cfg.Load(); err != nil {
		panic(fmt.Errorf("load config: %w", err))
	}

	var bc conf.Bootstrap
	if err := cfg.Scan(&bc); err != nil {
		panic(err)
	}

	if bc.Server == nil {
		bc.Server = &conf.Server{}
		_ = cfg.Scan(&bc.Server)
	}

	if bc.Data == nil {
		bc.Data = &conf.Data{}
		_ = cfg.Scan(&bc.Data)
	}

	if bc.Logger == nil {
		bc.Logger = &conf.Logger{}
		_ = cfg.Scan(&bc.Logger)
	}

	return &bc
}

func pathExists(path string) bool {
	_, err := os.Stat(path)
	if err == nil {
		return true
	}
	if os.IsNotExist(err) {
		return false
	}
	return false
}

// LoadRemoteConfigSourceConfigs 加载远程配置源的本地配置
func LoadRemoteConfigSourceConfigs(configPath string) (error, *conf.RemoteConfig) {
	// configPath = configPath
	if !pathExists(configPath) {
		return nil, nil
	}

	cfg := config.New(
		config.WithSource(
			NewFileConfigSource(configPath),
		),
	)
	defer func(cfg config.Config) {
		err := cfg.Close()
		if err != nil {
			panic(err)
		}
	}(cfg)

	var err error

	if err = cfg.Load(); err != nil {
		return err, nil
	}

	var rc conf.Bootstrap
	if err = cfg.Scan(&rc); err != nil {
		return err, nil
	}

	return nil, rc.Config
}

type ConfigType string

const (
	ConfigTypeLocalFile ConfigType = "file"
	ConfigTypeNacos     ConfigType = "nacos"
)

// NewRemoteConfigSource 创建一个远程配置源
func NewRemoteConfigSource(c *conf.RemoteConfig) config.Source {
	switch ConfigType(c.Type) {
	default:
		fallthrough
	case ConfigTypeLocalFile:
		return nil
	case ConfigTypeNacos:
		return NewNacosConfigSource(c)
	}
}

// getConfigKey 获取合法的配置名
func getConfigKey(configKey string, useBackslash bool) string {
	if useBackslash {
		return strings.Replace(configKey, `.`, `/`, -1)
	} else {
		return configKey
	}
}

// NewFileConfigSource 创建一个本地文件配置源
func NewFileConfigSource(filePath string) config.Source {
	return fileKratos.NewSource(filePath)
}

// NewNacosConfigSource 创建一个远程配置源 - Nacos
func NewNacosConfigSource(c *conf.RemoteConfig) config.Source {
	namespace := os.Getenv("MY_POD_NAMESPACE")
	if namespace == "" {
		namespace = c.Nacos.Namespace
	}
	address := os.Getenv("NACOS_SERVER_ADDRESS")
	if address == "" {
		address = c.Nacos.Address
	}
	accessKey := os.Getenv("NACOS_ACCESS_KEY")
	if accessKey == "" {
		accessKey = c.Nacos.AccessKey
	}
	secretKey := os.Getenv("NACOS_SECRET_KEY")
	if secretKey == "" {
		secretKey = c.Nacos.SecretKey
	}

	srvConf := []nacosConstant.ServerConfig{
		*nacosConstant.NewServerConfig(address, c.Nacos.Port),
	}

	cliConf := nacosConstant.ClientConfig{
		TimeoutMs:            10 * 1000, // http请求超时时间，单位毫秒
		BeatInterval:         5 * 1000,  // 心跳间隔时间，单位毫秒
		UpdateThreadNum:      20,        // 更新服务的线程数
		LogLevel:             "debug",
		CacheDir:             "./.nacos/cache", // 缓存目录
		LogDir:               "./.nacos/log",   // 日志目录
		NotLoadCacheAtStart:  true,             // 在启动时不读取本地缓存数据，true--不读取，false--读取
		UpdateCacheWhenEmpty: true,             // 当服务列表为空时是否更新本地缓存，true--更新,false--不更新
		NamespaceId:          namespace,        // 命名空间ID
		AccessKey:            accessKey,        // 访问密钥 key
		SecretKey:            secretKey,        // 访问密钥 secret
	}

	nacosClient, err := nacosClients.NewConfigClient(
		nacosVo.NacosClientParam{
			ClientConfig:  &cliConf,
			ServerConfigs: srvConf,
		},
	)
	if err != nil {
		log.Fatal(err)
	}

	return nacosKratos.NewConfigSource(nacosClient,
		nacosKratos.WithGroup(getConfigKey(c.Nacos.Group, false)),
		nacosKratos.WithDataID(c.Nacos.DataId),
	)
}
