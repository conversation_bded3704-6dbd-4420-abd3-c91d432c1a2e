// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/registry"
	"sh_proxy/app/consumer/internal/biz"
	"sh_proxy/app/consumer/internal/conf"
	"sh_proxy/app/consumer/internal/data"
	"sh_proxy/app/consumer/internal/server"
	"sh_proxy/app/consumer/internal/service"
)

import (
	_ "go.uber.org/automaxprocs"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireApp(confServer *conf.Server, confData *conf.Data, bootstrap *conf.Bootstrap, logger log.Logger, registrar registry.Registrar) (*kratos.App, func(), error) {
	mqConn := data.NewRabbitMQClient(bootstrap, logger)
	client, err := data.NewMongoClient(bootstrap, logger)
	if err != nil {
		return nil, nil, err
	}
	dataData, cleanup, err := data.NewData(confData, logger, mqConn, client)
	if err != nil {
		return nil, nil, err
	}
	consumerUsecase := biz.NewConsumerUsecase(dataData)
	consumerWebService := service.NewConsumerWebService(consumerUsecase)
	httpServer := server.NewHTTPServer(confServer, consumerWebService, logger)
	grpcServer := server.NewGRPCServer(confServer, consumerWebService, logger)
	ipInfoRepo := data.NewIPInfoRepo(dataData, bootstrap, logger)
	deviceRepo := data.NewDeviceRepo(dataData)
	eventLogRepo := data.NewEventLogRepo(dataData, bootstrap, logger)
	userUsecase := biz.NewUserUsecase(logger, dataData, ipInfoRepo, deviceRepo, eventLogRepo)
	consumerService, err := service.NewConsumerService(userUsecase, dataData, logger)
	if err != nil {
		cleanup()
		return nil, nil, err
	}
	consumerServer := server.NewConsumerServer(consumerService, logger)
	app := newApp(logger, httpServer, grpcServer, consumerServer, registrar)
	return app, func() {
		cleanup()
	}, nil
}
