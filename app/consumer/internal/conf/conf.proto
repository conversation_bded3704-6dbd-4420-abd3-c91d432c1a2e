syntax = "proto3";
package kratos.api;

option go_package = "sh_proxy/app/consumer/internal/conf;conf";

import "google/protobuf/duration.proto";

message Bootstrap {
  Server server = 1;
  Data data = 2;
  Logger logger = 3;
  RemoteConfig config = 4;
  Registry registry = 5;
}

message Server {
  message HTTP {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  message GRPC {
    string network = 1;
    string addr = 2;
    google.protobuf.Duration timeout = 3;
  }
  HTTP http = 1;
  GRPC grpc = 2;
}

message Data {
  message Database {
    string driver = 1;
    string source = 2;
  }
  message Redis {
    string network = 1;
    string addr = 2;
    string username = 3;
    string password = 4;
    int32 db = 5;
    google.protobuf.Duration dial_timeout = 6;
    google.protobuf.Duration read_timeout = 7;
    google.protobuf.Duration write_timeout = 8;
  }
  message MongoDB {
    string uri = 1;
    string db_name = 2;
    google.protobuf.Duration timeout = 3;
  }
  message RabbitMQ {
    string addr = 1;
    string user = 2;
    string password = 3;
    string vhost = 4;
    string exchange = 5;
    string queue = 6;
    string routing_key = 7;
  }

  Database database = 1;
  Redis redis = 2;
  RabbitMQ rabbitmq = 3;
  MongoDB mongodb = 4;
}

// 日志配置
message Logger {
  // Zap
  message Zap {
    string filename = 1;    // 基础文件名
    string level = 2;       // 日志级别
    int32 max_size = 3;     // 单个文件最大大小(MB)
    int32 max_age = 4;      // 日志保留天数
    int32 max_backups = 5;  // 保留的旧日志文件最大数量
    bool split_by_level = 6; // 是否按日志级别分别存储
    string log_dir = 7;     // 日志根目录，如果为空，将使用Filename所在目录
  }
  string type = 1;

  Zap zap = 2;
}

// 配置服务
message RemoteConfig {
  message Nacos {
    string address = 1; // 服务端地址
    uint64 port = 2; // 服务端端口
    string group = 3; //
    string data_id = 4; //
    string namespace = 5; //
    string access_key = 6; // 访问key
    string secret_key = 7; // 访问密钥
  }

  string type = 1;

  Nacos nacos = 2;
}

// 注册发现中心
message Registry {
  // Consul
  message Consul {
    string scheme = 1;  // 网络样式
    string address = 2; // 服务端地址
    bool health_check = 3;  // 健康检查
  }

  // Kubernetes
  message Kubernetes {

  }
  string type = 1;

  Consul consul = 2;  // Consul
  Kubernetes kubernetes = 6;  // Kubernetes
}
