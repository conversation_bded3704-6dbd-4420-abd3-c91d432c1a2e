package data

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
	"go.mongodb.org/mongo-driver/mongo"

	"sh_proxy/app/consumer/internal/conf"
	contextlog "sh_proxy/pkg/log"
	"sh_proxy/pkg/types"
)

type EventLogRepo interface {
	SaveEventLog(ctx context.Context, event *types.UserEvent) error
}

type eventLogRepo struct {
	data *Data
	log  *contextlog.Helper

	mdb *mongo.Database
}

func NewEventLogRepo(data *Data, cfg *conf.Bootstrap, logger log.Logger) EventLogRepo {
	return &eventLogRepo{
		data: data,
		log:  contextlog.NewHelper(logger, "module", "data/eventlog"),
		mdb:  data.mdb.Database(cfg.Data.Mongodb.DbName),
	}
}

func (r *eventLogRepo) SaveEventLog(ctx context.Context, event *types.UserEvent) error {
	collection := r.mdb.Collection("user_event_log")

	_, err := collection.InsertOne(ctx, event)
	if err != nil {
		r.log.WithContext(ctx).Errorw("err_msg", "failed to save event log",
			"event", event.MarshalStr(),
			"err", err)
		return err
	}

	return nil
}
