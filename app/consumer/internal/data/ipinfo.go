package data

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	"sh_proxy/app/consumer/internal/conf"
	contextlog "sh_proxy/pkg/log"
)

// IPInfo IP信息结构体
type IPInfo struct {
	IP          string    `bson:"_id" json:"ip"`
	Country     string    `bson:"country" json:"country"`
	CountryCode string    `bson:"countryCode" json:"countryCode"`
	Region      string    `bson:"region" json:"region"`
	RegionName  string    `bson:"regionName" json:"regionName"`
	City        string    `bson:"city" json:"city"`
	District    string    `bson:"district" json:"district"`
	Zip         string    `bson:"zip" json:"zip"`
	Lat         float64   `bson:"lat" json:"lat"`
	Lon         float64   `bson:"lon" json:"lon"`
	Timezone    string    `bson:"timezone" json:"timezone"`
	ISP         string    `bson:"isp" json:"isp"`
	CreatedAt   time.Time `bson:"createdAt" json:"createdAt"`
	UpdatedAt   time.Time `bson:"updatedAt" json:"updatedAt"`
}

// GetLocation 获取格式化的位置信息
func (ip *IPInfo) GetLocation() string {
	parts := []string{}
	if ip.CountryCode != "" {
		parts = append(parts, ip.CountryCode)
	}
	if ip.RegionName != "" {
		parts = append(parts, ip.RegionName)
	}
	if ip.City != "" {
		parts = append(parts, ip.City)
	}
	return strings.Join(parts, ":")
}

// apiIPInfo 外部API返回的IP信息
type apiIPInfo struct {
	Status      string  `json:"status"`
	Query       string  `json:"query"`
	Country     string  `json:"country"`
	CountryCode string  `json:"countryCode"`
	Region      string  `json:"region"`
	RegionName  string  `json:"regionName"`
	City        string  `json:"city"`
	District    string  `json:"district"`
	Zip         string  `json:"zip"`
	Lat         float64 `json:"lat"`
	Lon         float64 `json:"lon"`
	Timezone    string  `json:"timezone"`
	Offset      int64   `json:"offset"`
	Currency    string  `json:"currency"`
	Proxy       bool    `json:"proxy"`
	Hosting     bool    `json:"hosting"`
	Asname      string  `json:"asname"`
	As          string  `json:"as"`
	ISP         string  `json:"isp"`
	Mobile      bool    `json:"mobile"`
	ReqTime     int64   `json:"req_time"`
	ReplyTime   int64   `json:"reply_time"`
}

// IPInfoRepo IP信息仓库接口
type IPInfoRepo interface {
	GetIPInfo(ctx context.Context, ip string) (*IPInfo, error)
	SaveIPInfo(ctx context.Context, ipInfo *IPInfo) error
}

type ipInfoRepo struct {
	data *Data
	log  *contextlog.Helper

	mdb         *mongo.Database
	lockManager *LockManager
}

// NewIPInfoRepo 创建IP信息仓库
func NewIPInfoRepo(data *Data, cfg *conf.Bootstrap, logger log.Logger) IPInfoRepo {
	return &ipInfoRepo{
		data:        data,
		log:         contextlog.NewHelper(logger, "module", "data/ipinfo"),
		mdb:         data.mdb.Database(cfg.Data.Mongodb.DbName),
		lockManager: data.lockManager,
	}
}

// GetIPInfo 获取IP信息，先查MongoDB，再查外部API（使用分布式锁防止重复查询）
func (r *ipInfoRepo) GetIPInfo(ctx context.Context, ip string) (*IPInfo, error) {
	// 先从MongoDB查询
	ipInfo, err := r.getIPInfoFromMongo(ctx, ip)
	if err == nil && ipInfo != nil {
		return ipInfo, nil
	}

	// MongoDB中没有，使用分布式锁防止并发查询同一个IP
	lockKey := GetIPInfoLockKey(ip)
	lockTTL := 60 * time.Second // IP查询可能需要较长时间

	var result *IPInfo
	err = r.lockManager.WithLockRetry(ctx, lockKey, lockTTL, 3, 200*time.Millisecond, func() error {
		// 再次检查MongoDB，可能其他进程已经查询并保存了
		if cachedInfo, err := r.getIPInfoFromMongo(ctx, ip); err == nil && cachedInfo != nil {
			result = cachedInfo
			return nil
		}

		// 调用外部API
		apiInfo, err := r.getIPInfoFromAPI(ctx, ip)
		if err != nil {
			return fmt.Errorf("failed to get IP info from API: %w", err)
		}

		// 转换并保存到MongoDB
		result = &IPInfo{
			IP:          ip,
			Country:     apiInfo.Country,
			CountryCode: apiInfo.CountryCode,
			Region:      apiInfo.Region,
			RegionName:  apiInfo.RegionName,
			City:        apiInfo.City,
			District:    apiInfo.District,
			Zip:         apiInfo.Zip,
			Lat:         apiInfo.Lat,
			Lon:         apiInfo.Lon,
			Timezone:    apiInfo.Timezone,
			ISP:         apiInfo.ISP,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		}

		// 同步保存到MongoDB（在锁内完成）
		if err := r.SaveIPInfo(ctx, result); err != nil {
			r.log.WithContext(ctx).Errorf("failed to save IP info to MongoDB: %v", err)
			// 即使保存失败，也返回查询到的信息
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return result, nil
}

// getIPInfoFromMongo 从MongoDB获取IP信息
func (r *ipInfoRepo) getIPInfoFromMongo(ctx context.Context, ip string) (*IPInfo, error) {
	collection := r.mdb.Collection("ip_info")

	var ipInfo IPInfo
	err := collection.FindOne(ctx, bson.M{"_id": ip}).Decode(&ipInfo)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, nil
		}
		return nil, err
	}

	return &ipInfo, nil
}

// getIPInfoFromAPI 从外部API获取IP信息
func (r *ipInfoRepo) getIPInfoFromAPI(ctx context.Context, ip string) (*apiIPInfo, error) {
	// 调用 ip-api 接口
	// resp, err := http.Get(fmt.Sprintf("https://pro.ip-api.com/json/%s?key=7V5bSY01Oy8L8fb&fields=21179098", ip))
	resp, err := http.Get(fmt.Sprintf("https://pro.ip-api.com/json/%s?key=7V5bSY01Oy8L8fb&fields=66842623", ip))
	if err != nil {
		return nil, fmt.Errorf("API请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API请求失败: %s", resp.Status)
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	// 解析响应
	var apiResponse apiIPInfo
	if err := json.Unmarshal(body, &apiResponse); err != nil {
		return nil, fmt.Errorf("响应解析失败: %w", err)
	}
	if apiResponse.Status != "success" {
		return nil, fmt.Errorf("IPAPI返回失败: %s", string(body))
	}
	// if apiResponse.Country == "" {
	// 	return nil, fmt.Errorf("IPAPI返回失败: %s", string(body))
	// }

	return &apiResponse, nil
}

// SaveIPInfo 保存IP信息到MongoDB
func (r *ipInfoRepo) SaveIPInfo(ctx context.Context, ipInfo *IPInfo) error {
	collection := r.mdb.Collection("ip_info")

	opts := options.Replace().SetUpsert(true)
	_, err := collection.ReplaceOne(ctx, bson.M{"_id": ipInfo.IP}, ipInfo, opts)
	return err
}
