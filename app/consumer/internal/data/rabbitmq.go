package data

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"
	amqp "github.com/rabbitmq/amqp091-go"

	"sh_proxy/app/consumer/internal/conf"
	"sh_proxy/pkg/utils"
)

type MQConn struct {
	conn       *amqp.Connection
	ch         *amqp.Channel
	exchange   string
	queue      string
	routingKey string
	url        string
	connLock   sync.Mutex
	chLock     sync.Mutex
	closed     bool

	log       *log.Helper
	reconnect chan struct{}
	stopCh    chan struct{}
	consumeWg sync.WaitGroup
}

func NewRabbitMQClient(cfg *conf.Bootstrap, logger log.Logger) *MQConn {
	l := log.NewHelper(log.With(logger, "module", "rabbitmq/data/consumer"))

	// 检查配置是否有效
	if cfg.Data.Rabbitmq == nil || cfg.Data.Rabbitmq.Addr == "" {
		l.Fatalf("RabbitMQ配置不完整，跳过初始化")
		return nil
	}

	// 构建RabbitMQ连接URL
	url := cfg.Data.Rabbitmq.Addr + cfg.Data.Rabbitmq.Vhost

	mqConn := &MQConn{
		exchange:   cfg.Data.Rabbitmq.Exchange,
		queue:      cfg.Data.Rabbitmq.Queue,
		routingKey: cfg.Data.Rabbitmq.RoutingKey,
		url:        url,
		log:        l,
		reconnect:  make(chan struct{}, 1),
		stopCh:     make(chan struct{}),
	}

	// 初始化连接
	if err := mqConn.connect(); err != nil {
		l.Fatalf("初始化RabbitMQ连接失败: %v", err)
	} else {
		// 启动连接监控
		go mqConn.monitorConnection()
	}

	return mqConn
}

// 连接到RabbitMQ
func (m *MQConn) connect() error {
	m.connLock.Lock()
	defer m.connLock.Unlock()

	if m.closed {
		return errors.New("MQConn已关闭")
	}

	var err error
	// 创建连接
	m.conn, err = amqp.Dial(m.url)
	if err != nil {
		return fmt.Errorf("连接RabbitMQ失败: %w", err)
	}

	// 监听连接关闭
	closeChan := make(chan *amqp.Error, 1)
	m.conn.NotifyClose(closeChan)

	// 在新的goroutine中监听连接关闭事件
	go func() {
		closeErr := <-closeChan
		m.log.Infof("RabbitMQ连接关闭: %v", closeErr)

		// 如果不是主动关闭，则触发重连
		if !m.closed {
			m.triggerReconnect()
		}
	}()

	// 创建通道
	err = m.createChannel()
	if err != nil {
		m.conn.Close()
		return fmt.Errorf("创建通道失败: %w", err)
	}

	m.log.Infof("成功连接到RabbitMQ")
	return nil
}

// 创建通道
func (m *MQConn) createChannel() error {
	m.chLock.Lock()
	defer m.chLock.Unlock()

	var err error
	m.ch, err = m.conn.Channel()
	if err != nil {
		return err
	}

	// 监听通道关闭
	chCloseChan := make(chan *amqp.Error, 1)
	m.ch.NotifyClose(chCloseChan)

	// 在新的goroutine中监听通道关闭事件
	go func() {
		closeErr := <-chCloseChan
		m.log.Infof("RabbitMQ通道关闭: %v", closeErr)

		// 如果不是主动关闭，则尝试重新创建通道
		if !m.closed {
			// 检查连接是否仍然有效
			if !m.conn.IsClosed() {
				if err := m.createChannel(); err != nil {
					m.log.Errorf("重建通道失败: %v", err)
					m.triggerReconnect() // 如果创建通道失败，则触发完整重连
				} else {
					m.log.Infof("成功重建通道")
				}
			}
		}
	}()

	// 声明交换机
	if m.exchange != "" {
		err = m.ch.ExchangeDeclare(
			m.exchange, // 交换机名称
			"topic",    // 类型
			true,       // 持久化
			false,      // 自动删除
			false,      // 内部的
			false,      // 非阻塞
			nil,        // 参数
		)
		if err != nil {
			m.ch.Close()
			return fmt.Errorf("声明交换机失败: %w", err)
		}
	}

	// 声明队列
	if m.queue != "" {
		_, err = m.ch.QueueDeclare(
			m.queue, // 队列名称
			true,    // 持久化
			false,   // 自动删除
			false,   // 排他性
			false,   // 非阻塞
			nil,     // 参数
		)
		if err != nil {
			m.ch.Close()
			return fmt.Errorf("声明队列失败: %w", err)
		}

		// 绑定队列到交换机
		if m.exchange != "" && m.routingKey != "" {
			err = m.ch.QueueBind(
				m.queue,      // 队列名称
				m.routingKey, // 路由键
				m.exchange,   // 交换机
				false,        // 非阻塞
				nil,          // 参数
			)
			if err != nil {
				m.ch.Close()
				return fmt.Errorf("绑定队列到交换机失败: %w", err)
			}
		}
	}

	return nil
}

// 触发重连
func (m *MQConn) triggerReconnect() {
	select {
	case m.reconnect <- struct{}{}:
		// 成功发送重连信号
	default:
		// 重连通道已满，说明已经有重连信号了
	}
}

// 监控连接状态并处理重连
func (m *MQConn) monitorConnection() {
	maxConsecutiveAttempts := 30 // 最大连续尝试次数，用于降低日志频率
	consecutiveAttempts := 0     // 当前连续尝试次数

	for {
		select {
		case <-m.reconnect:
			// 指数退避重连
			success := false
			for attempt := 0; attempt < 10; attempt++ {
				// 只有在尝试次数不太多的情况下才记录详细日志
				if consecutiveAttempts < maxConsecutiveAttempts {
					m.log.Infof("尝试重连RabbitMQ (尝试 %d)", attempt+1)
				} else if attempt == 0 {
					// 如果已经尝试了很多次，只在开始新一轮重试时打印日志
					m.log.Warnf("尝试重连RabbitMQ (已尝试 %d 次以上)", consecutiveAttempts)
				}

				err := m.connect()
				if err == nil {
					m.log.Infof("成功重连到RabbitMQ")
					success = true
					consecutiveAttempts = 0 // 重置连续尝试计数器
					break
				}

				// 计算退避时间
				backoff := time.Duration(1<<uint(attempt)) * time.Second
				if backoff > 30*time.Second {
					backoff = 30 * time.Second
				}

				// 只有在尝试次数不太多的情况下才记录详细日志
				if consecutiveAttempts < maxConsecutiveAttempts {
					m.log.Errorf("重连失败: %v, 将在 %v 后重试", err, backoff)
				}

				select {
				case <-time.After(backoff):
					// 继续下一次尝试
				case <-m.stopCh:
					// 收到停止信号
					return
				}
			}

			// 如果这一轮所有尝试都失败了
			if !success {
				consecutiveAttempts++
				// 再次触发重连
				m.triggerReconnect()
			}
		case <-m.stopCh:
			// 收到停止信号，退出监控
			return
		}
	}
}

// Close 关闭连接
func (m *MQConn) Close() error {
	m.connLock.Lock()
	defer m.connLock.Unlock()

	if m.closed {
		return nil
	}

	m.closed = true
	close(m.stopCh) // 通知所有goroutine停止

	// 等待所有消费者停止
	m.consumeWg.Wait()

	// 关闭通道和连接
	var err error
	if m.ch != nil {
		err = m.ch.Close()
	}

	if m.conn != nil {
		connErr := m.conn.Close()
		if err == nil {
			err = connErr
		}
	}

	m.log.Info("RabbitMQ连接已关闭")
	return err
}

// ConsumeMessages 消费队列消息
func (m *MQConn) ConsumeMessages(ctx context.Context, queue string, handler func([]byte) error) error {
	if m.closed {
		return errors.New("MQConn已关闭")
	}

	m.chLock.Lock()
	if m.ch == nil {
		m.chLock.Unlock()
		return errors.New("RabbitMQ通道未初始化")
	}
	m.chLock.Unlock()

	// 增加消费者计数
	m.consumeWg.Add(1)
	defer m.consumeWg.Done()

	for {
		// 检查上下文是否已取消
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-m.stopCh:
			return errors.New("连接已关闭")
		default:
			// 继续处理
		}

		m.chLock.Lock()
		ch := m.ch
		m.chLock.Unlock()

		// 检查通道
		if ch == nil {
			// 等待重新连接
			select {
			case <-time.After(time.Second):
				continue
			case <-ctx.Done():
				return ctx.Err()
			case <-m.stopCh:
				return errors.New("连接已关闭")
			}
		}

		// 开始消费
		deliveries, err := ch.Consume(
			queue,                  // 队列
			getConsumerName(queue), // 消费者标签（空字符串表示自动生成）
			false,                  // 自动确认
			false,                  // 排他性
			false,                  // 不要等待服务器确认
			false,                  // 非阻塞
			nil,                    // 参数
		)

		if err != nil {
			m.log.Errorf("开始消费队列失败: %v", err)
			// 等待重连
			select {
			case <-time.After(time.Second):
				continue
			case <-ctx.Done():
				return ctx.Err()
			case <-m.stopCh:
				return errors.New("连接已关闭")
			}
		}

		m.log.Infof("开始消费队列: %s", queue)

		// 添加一个退出标志，用于在通道关闭时跳出当前循环
		deliveryClosed := false

		// 处理消息
		for !deliveryClosed {
			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-m.stopCh:
				return errors.New("连接已关闭")
			case msg, ok := <-deliveries:
				if !ok {
					m.log.Warnf("消费者通道已关闭，准备重新连接")
					deliveryClosed = true // 设置标志，跳出当前循环
					// 触发重连
					// m.triggerReconnect()
					// 等待一秒后重试，避免立即重连造成的资源消耗
					time.Sleep(time.Second)
					break
				}

				// 处理消息
				err := handler(msg.Body)
				if err != nil {
					m.log.Errorf("处理消息失败: %v", err)
					// 消息处理失败，拒绝并重新入队
					// msg.Reject(true)
				} else {
					// 确认消息
				}
				if err := msg.Ack(false); err != nil {
					m.log.Errorf("确认消息失败: %v", err)
				}
			}
		}

		// 如果是因为通道关闭而退出，则记录并继续外层循环
		m.log.Infof("消费循环退出，准备重新开始消费")
	}
}

func getConsumerName(queue string) string {
	return utils.GetNamespace() + "-" + queue + "-consumer"
}

// PublishMessage 发布消息到指定队列
func (m *MQConn) PublishMessage(exchange, routingKey string, body []byte) error {
	m.chLock.Lock()
	defer m.chLock.Unlock()

	if m.ch == nil {
		return errors.New("RabbitMQ通道未初始化")
	}

	// 如果没有指定交换机，使用默认队列
	if exchange == "" && routingKey == "default" {
		routingKey = "default"
	}

	return m.ch.Publish(
		exchange,   // exchange
		routingKey, // routing key
		false,      // mandatory
		false,      // immediate
		amqp.Publishing{
			ContentType:  "application/json",
			Body:         body,
			DeliveryMode: amqp.Persistent, // 持久化消息
		},
	)
}

func (m *MQConn) GetQueue() string {
	return m.queue
}
