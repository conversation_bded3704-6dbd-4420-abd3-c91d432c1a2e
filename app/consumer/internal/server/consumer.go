package server

import (
	"context"

	"sh_proxy/app/consumer/internal/service"

	"github.com/go-kratos/kratos/v2/log"
)

// ConsumerServer 任务结果消费服务器
type ConsumerServer struct {
	consumer *service.ConsumerService
	log      *log.Helper
}

// NewConsumerServer 创建任务结果消费服务器
func NewConsumerServer(consumer *service.ConsumerService, logger log.Logger) *ConsumerServer {
	return &ConsumerServer{
		consumer: consumer,
		log:      log.NewHelper(log.With(logger, "module", "server/consumer")),
	}
}

// Start 启动消费服务器
func (s *ConsumerServer) Start(_ context.Context) error {
	s.log.Info("启动任务结果消费服务器")
	return s.consumer.Start()
}

// Stop 停止消费服务器
func (s *ConsumerServer) Stop(_ context.Context) error {
	s.log.Info("停止任务结果消费服务器")
	s.consumer.Stop()
	return nil
}

// Name 服务器名称
func (s *ConsumerServer) Name() string {
	return "consumer.server"
}
