package service

import (
	"context"

	v1 "sh_proxy/api/consumer/v1"
	"sh_proxy/app/consumer/internal/biz"
)

type ConsumerWebService struct {
	v1.UnimplementedConsumerServer
	cu *biz.ConsumerUsecase
}

func NewConsumerWebService(ConsumerUsecase *biz.ConsumerUsecase) *ConsumerWebService {
	return &ConsumerWebService{
		cu: ConsumerUsecase,
	}
}

func (s *ConsumerWebService) Health(ctx context.Context, req *v1.Empty) (*v1.Empty, error) {
	return &v1.Empty{}, nil
}
