package service

import (
	"context"
	"fmt"

	"github.com/go-kratos/kratos/v2/log"

	"sh_proxy/app/consumer/internal/biz"
	"sh_proxy/app/consumer/internal/data"
	contextlog "sh_proxy/pkg/log"
	"sh_proxy/pkg/types"
)

// UserEventHandler 用户事件处理器
type UserEventHandler struct {
	log *contextlog.Helper
	uc  *biz.UserUsecase
}

func (h *UserEventHandler) Type() string {
	return types.MQMessageTypeUserEvent
}

func (h *UserEventHandler) Handle(ctx context.Context, msg *types.MQMessage) error {
	// 解析用户事件
	userEvent, err := msg.UnmarshalUserEvent()
	if err != nil {
		return fmt.Errorf("解析用户事件失败: %w", err)
	}

	h.log.WithContext(ctx).Infow("msg", "处理用户事件",
		"event_type", userEvent.Name,
		"sdk_id", userEvent.SdkId,
		"sdk_ip", userEvent.SdkIp,
		"conn_id", userEvent.ConnId,
		"supplier", userEvent.Supplier,
	)

	// 根据事件类型分发处理
	switch userEvent.Name {
	case types.UserEventConnect:
		return h.handleConnectEvent(ctx, userEvent, msg.TraceId)
	case types.UserEventDisconnect:
		return h.handleDisconnectEvent(ctx, userEvent, msg.TraceId)
	default:
		return fmt.Errorf("未知的用户事件类型: %s", userEvent.Name)
	}
}

// handleConnectEvent 处理连接事件
func (h *UserEventHandler) handleConnectEvent(ctx context.Context, event *types.UserEvent, traceId string) error {
	return h.uc.HandleConnect(ctx, event)
}

// handleDisconnectEvent 处理断连事件
func (h *UserEventHandler) handleDisconnectEvent(ctx context.Context, event *types.UserEvent, traceId string) error {
	return h.uc.HandleDisconnect(ctx, event)
}

// TaskResultHandler 任务结果处理器（保持原有功能）
type TaskResultHandler struct {
	log  *log.Helper
	data *data.Data
}

func (h *TaskResultHandler) Type() string {
	return types.MQMessageTypeTaskResult
}

func (h *TaskResultHandler) Handle(ctx context.Context, msg *types.MQMessage) error {
	h.log.Infow("msg", "处理任务结果",
		"trace_id", msg.TraceId,
		"msg", msg.Message,
	)

	// TODO: 实现任务结果处理逻辑
	return nil
}
