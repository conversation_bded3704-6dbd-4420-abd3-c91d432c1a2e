// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/registry"
	"sh_proxy/app/user/internal/biz"
	"sh_proxy/app/user/internal/conf"
	"sh_proxy/app/user/internal/data"
	"sh_proxy/app/user/internal/server"
	"sh_proxy/app/user/internal/service"
)

import (
	_ "go.uber.org/automaxprocs"
)

// Injectors from wire.go:

// wireApp init kratos application.
func wireApp(confServer *conf.Server, bootstrap *conf.Bootstrap, logger log.Logger, registrar registry.Registrar) (*kratos.App, func(), error) {
	client, err := data.NewMongoClient(bootstrap, logger)
	if err != nil {
		return nil, nil, err
	}
	mqConn := data.NewRabbitMQClient(bootstrap, logger)
	dataData, cleanup, err := data.NewData(bootstrap, logger, client, mqConn)
	if err != nil {
		return nil, nil, err
	}
	ipInfoRepo := data.NewIPInfoRepo(dataData, bootstrap, logger)
	deviceRepo := data.NewDeviceRepo(dataData)
	userUsecase := biz.NewUserUsecase(logger, dataData, ipInfoRepo, deviceRepo)
	eventPublisher := service.NewMQEventPublisher(logger, dataData)
	userService := service.NewUserService(logger, userUsecase, eventPublisher)
	grpcServer := server.NewGRPCServer(confServer, userService, logger)
	httpServer := server.NewHTTPServer(confServer, userService, logger)
	app := newApp(logger, grpcServer, httpServer, registrar)
	return app, func() {
		cleanup()
	}, nil
}
