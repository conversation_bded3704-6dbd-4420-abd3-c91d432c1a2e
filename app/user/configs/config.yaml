server:
  http:
    addr: 0.0.0.0:8020
    timeout: 20s
  grpc:
    addr: 0.0.0.0:9020
    timeout: 20s
data:
  database:
    driver: mysql
    source: root:123456@tcp(127.0.0.1:3306)/test?parseTime=True&loc=Local
  redis:
    addr: 127.0.0.1:6379
    password: ''
    Db: 0
    dial_timeout: 2s
    read_timeout: 2s
    write_timeout: 2s
  cr_redis:
    addr: 127.0.0.1:6379
    password: ''
    Db: 0
    dial_timeout: 2s
    read_timeout: 2s
    write_timeout: 2s
  mongodb:
    uri: "mongodb://mf_gateway_rw:<EMAIL>:3717,dds-t4n80f98ba851c642368-pub.mongodb.singapore.rds.aliyuncs.com:3717/?replicaSet=mgset-309552195&authSource=dynamic_proxy_gateway_dev&readPreference=secondaryPreferred"
    timeout: 10s
    db_name: "dynamic_proxy_gateway_dev"
logger:
  type: zap
  zap:
    level: "debug"
    filename: "sh_user.log"
    max_size: 500
    max_age: 30
    max_backups: 5
    split_by_level: true
    log_dir: "./logs"
registry:
  type: "consul"
  consul:
    address: "127.0.0.1:8500"
    scheme: "http"
    health_check: false