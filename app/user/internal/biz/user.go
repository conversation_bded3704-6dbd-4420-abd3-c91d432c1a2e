package biz

import (
	"context"
	"fmt"
	"math/rand"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/pkg/errors"

	v1 "sh_proxy/api/user/v1"
	"sh_proxy/app/user/internal/data"
	contextlog "sh_proxy/app/user/internal/pkg/log"
)

type UserUsecase struct {
	log *contextlog.Helper

	data       *data.Data
	ipInfoRepo data.IPInfoRepo
	deviceRepo data.DeviceRepo
}

func NewUserUsecase(logger log.Logger, data *data.Data, ipInfoRepo data.IPInfoRepo, deviceRepo data.DeviceRepo) *UserUsecase {
	return &UserUsecase{
		log:        contextlog.NewHelper(logger, "module", "biz/user"),
		data:       data,
		ipInfoRepo: ipInfoRepo,
		deviceRepo: deviceRepo,
	}
}

func (ju *UserUsecase) Event(ctx context.Context, req *v1.EventRequest) (*v1.EventReply, error) {
	switch req.Name {
	case EventTypeConnect:
		return ju.handleConnect(ctx, req)
	case EventTypeDisconnect:
		return ju.handleDisconnect(ctx, req)
	default:
		return &v1.EventReply{
			Message: fmt.Sprintf("unknown event type: %s", req.Name),
		}, nil
	}
}

// handleConnect 处理连接事件
func (ju *UserUsecase) handleConnect(ctx context.Context, req *v1.EventRequest) (*v1.EventReply, error) {
	// 使用 WithContext 自动注入 trace_id
	ju.log.WithContext(ctx).Infow("msg", "Handling connect event",
		"sdk_id", req.SdkId,
		"sdk_ip", req.SdkIp,
		"conn_id", req.ConnId,
		"host", req.Host,
		"supplier", req.Supplier,
	)

	// 1. 查询IP归属地信息
	ipInfo, err := ju.ipInfoRepo.GetIPInfo(ctx, req.SdkIp)
	if err != nil {
		ju.log.WithContext(ctx).Errorw("msg", "Failed to get IP info",
			"sdk_ip", req.SdkIp,
			"error", err,
		)
		return &v1.EventReply{
			Message: fmt.Sprintf("failed to get IP info: %v", err),
		}, err
	}

	// 2. 创建设备信息
	device := &data.DeviceInfo{
		SdkIP:     req.SdkIp,
		Supplier:  req.Supplier,
		Location:  ipInfo.GetLocation(),
		CreatedAt: time.Now(),
	}

	// 3. 添加设备到在线列表
	if err := ju.deviceRepo.AddDevice(ctx, device, req.SdkId, &data.ConnInfo{
		Host:   req.Host,
		ConnId: req.ConnId,
	}); err != nil {
		ju.log.WithContext(ctx).Errorw("msg", "Failed to add device",
			"sdk_id", req.SdkId,
			"sdk_ip", req.SdkIp,
			"conn_id", req.ConnId,
			"error", err,
		)
		return &v1.EventReply{
			Message: fmt.Sprintf("failed to add device: %v", err),
		}, err
	}

	// 4. 异步记录连接日志
	go ju.logConnection(req, ipInfo)

	return &v1.EventReply{
		Message: "device connected successfully",
	}, nil
}

// handleDisconnect 处理断连事件
func (ju *UserUsecase) handleDisconnect(ctx context.Context, req *v1.EventRequest) (*v1.EventReply, error) {
	ju.log.WithContext(ctx).Infow("msg", "Handling disconnect event",
		"sdk_id", req.SdkId,
		"sdk_ip", req.SdkIp,
		"conn_id", req.ConnId,
		"supplier", req.Supplier,
	)

	// 从在线设备中移除连接
	if err := ju.deviceRepo.RemoveDevice(ctx, req.Supplier, req.SdkId, req.SdkIp, req.ConnId); err != nil {
		ju.log.WithContext(ctx).Errorw("msg", "Failed to remove device",
			"sdk_id", req.SdkId,
			"sdk_ip", req.SdkIp,
			"conn_id", req.ConnId,
			"error", err,
		)
		return &v1.EventReply{
			Message: fmt.Sprintf("failed to remove device: %v", err),
		}, err
	}

	// 异步记录断连日志
	go ju.logDisconnection(req)

	return &v1.EventReply{
		Message: "device disconnected successfully",
	}, nil
}

// logConnection 异步记录连接日志
func (ju *UserUsecase) logConnection(req *v1.EventRequest, ipInfo *data.IPInfo) {
	// 注意：这里是异步调用，没有原始的 context，所以使用普通的日志方法
	ju.log.Infow(
		"msg", "Connection log",
		"sdk_id", req.SdkId,
		"sdk_ip", req.SdkIp,
		"conn_id", req.ConnId,
		"location", ipInfo.GetLocation(),
		"country", ipInfo.Country,
		"city", ipInfo.City,
	)
	// TODO: 这里可以添加更详细的日志记录逻辑，比如写入数据库或日志文件
}

// logDisconnection 异步记录断连日志
func (ju *UserUsecase) logDisconnection(req *v1.EventRequest) {
	// 注意：这里是异步调用，没有原始的 context，所以使用普通的日志方法
	ju.log.Infow(
		"msg", "Connection log",
		"sdk_id", req.SdkId,
		"sdk_ip", req.SdkIp,
		"conn_id", req.ConnId,
	)
	// TODO: 这里可以添加更详细的日志记录逻辑，比如计算在线时长
}

func (ju *UserUsecase) Route(ctx context.Context, req *v1.RouteRequest) (*v1.RouteReply, error) {
	ju.log.WithContext(ctx).Infow("msg", "Handling route request",
		"location", req.Location,
		"auth_user", req.AuthUser,
		"session", req.Session,
		"host", req.Host,
		"port", req.Port,
	)

	// 1. 检查会话粘性
	if req.Session != "" {
		return nil, errors.New("session sticky mode not implemented")
	}

	// 2. 根据位置选择设备
	var devices []*data.DeviceInfo
	var err error
	if req.Location == "" {
		// 混播模式：基于权重随机选择一个设备
		selectedDevice, err := ju.getRandomDeviceForMixedMode(ctx, req.Supplier)
		if err != nil {
			ju.log.WithContext(ctx).Errorw("msg", "Failed to get device for mixed mode",
				"supplier", req.Supplier,
				"error", err,
			)
			return nil, fmt.Errorf("failed to get device for mixed mode: %w", err)
		}
	} else {
		// 指定位置模式：精确匹配或模糊匹配
		devices, err = ju.getDevicesByLocation(ctx, req.Supplier, req.Location)
	}

	if err != nil {
		ju.log.WithContext(ctx).Errorw("msg", "Failed to get devices",
			"location", req.Location,
			"error", err,
		)
		return nil, fmt.Errorf("failed to get devices: %w", err)
	}

	selectedSdkID, selectedConnInfo := ju.selectBestConnection(selectedDevice)

	ju.log.WithContext(ctx).Infow("msg", "Selected device and connection",
		"sdk_id", selectedSdkID,
		"sdk_ip", selectedDevice.SdkIP,
		"host", selectedConnInfo.Host,
		"conn_id", selectedConnInfo.ConnId,
		"location", selectedDevice.Location,
	)

	return &v1.RouteReply{
		Host:   selectedConnInfo.Host,
		SkdId:  selectedSdkID,
		SdkIp:  selectedDevice.SdkIP,
		ConnId: selectedConnInfo.ConnId,
	}, nil
}

// getDevicesByLocation 根据位置获取设备（支持模糊匹配）
func (ju *UserUsecase) getDevicesByLocation(ctx context.Context, supplier, location string) ([]*data.DeviceInfo, error) {
	// 先尝试精确匹配
	devices, err := ju.deviceRepo.GetDevicesByLocation(ctx, supplier, location)
	if err != nil {
		return nil, err
	}

	if len(devices) > 0 {
		ju.log.WithContext(ctx).Infow("msg", "Found devices using exact location",
			"supplier", supplier,
			"location", location,
			"device_count", len(devices),
		)
		return devices, nil
	}

	// 如果精确匹配没有结果，尝试模糊匹配
	// 例如：US_Florida_Miami -> US_Florida -> US
	parts := strings.Split(location, "_")
	for i := len(parts) - 1; i > 0; i-- {
		partialLocation := strings.Join(parts[:i], "_")
		devices, err = ju.deviceRepo.GetDevicesByLocation(ctx, supplier, partialLocation)
		if err != nil {
			continue
		}
		if len(devices) > 0 {
			ju.log.WithContext(ctx).Infow(
				"msg", "Found devices using partial location",
				"supplier", supplier,
				"original_location", location,
				"partial_location", partialLocation,
				"device_count", len(devices),
			)
			return devices, nil
		}
	}

	ju.log.WithContext(ctx).Warnw(
		"msg", "No devices found for location",
		"supplier", supplier,
		"location", location,
	)
	return devices, nil
}

// getRandomDeviceForMixedMode 混播模式：基于权重随机选择一个设备（高效版本）
func (ju *UserUsecase) getRandomDeviceForMixedMode(ctx context.Context, supplier string) (*data.DeviceInfo, error) {
	ju.log.WithContext(ctx).Infow("msg", "Getting device for mixed mode",
		"supplier", supplier,
	)

	device, err := ju.deviceRepo.GetRandomDeviceForMixedMode(ctx, supplier)
	if err != nil {
		return nil, fmt.Errorf("failed to get random device: %w", err)
	}

	ju.log.WithContext(ctx).Infow("msg", "Selected device for mixed mode",
		"supplier", supplier,
		"sdk_ip", device.SdkIP,
		"location", device.Location,
	)

	return device, nil
}

// selectDevice 智能负载均衡选择设备
func (ju *UserUsecase) selectDevice(devices []*data.DeviceInfo) *data.DeviceInfo {
	if len(devices) == 1 {
		return devices[0]
	}

	// 计算每个设备的负载分数（连接数越少分数越高）
	type deviceScore struct {
		device *data.DeviceInfo
		score  float64
		sdkID  string
	}

	var deviceScores []deviceScore

	for _, device := range devices {
		totalConnections := 0
		var selectedSdkID string

		// 计算设备的总连接数，选择连接数最少的 SDK
		minConnections := int(^uint(0) >> 1) // 最大整数
		for sdkID, connInfos := range device.SdkConn {
			connCount := len(connInfos)
			totalConnections += connCount

			if connCount < minConnections {
				minConnections = connCount
				selectedSdkID = sdkID
			}
		}

		if selectedSdkID == "" {
			continue // 没有可用的 SDK
		}

		// 计算负载分数：连接数越少分数越高
		// 同时考虑设备的最后活跃时间
		timeFactor := 1.0
		if time.Since(device.LastSeen) > 5*time.Minute {
			timeFactor = 0.5 // 长时间未活跃的设备降低优先级
		}

		// 分数 = 基础分数 / (连接数 + 1) * 时间因子
		score := 100.0 / float64(totalConnections+1) * timeFactor

		deviceScores = append(deviceScores, deviceScore{
			device: device,
			score:  score,
			sdkID:  selectedSdkID,
		})
	}

	if len(deviceScores) == 0 {
		// 如果没有可用设备，随机选择一个
		rand.Seed(time.Now().UnixNano())
		return devices[rand.Intn(len(devices))]
	}

	// 基于分数进行加权随机选择
	totalScore := 0.0
	for _, ds := range deviceScores {
		totalScore += ds.score
	}

	if totalScore <= 0 {
		// 如果总分数为0，随机选择
		rand.Seed(time.Now().UnixNano())
		return deviceScores[rand.Intn(len(deviceScores))].device
	}

	// 加权随机选择
	rand.Seed(time.Now().UnixNano())
	randomScore := rand.Float64() * totalScore
	currentScore := 0.0

	for _, ds := range deviceScores {
		currentScore += ds.score
		if randomScore <= currentScore {
			return ds.device
		}
	}

	// 兜底：返回第一个设备
	return deviceScores[0].device
}

// selectBestConnection 从设备中选择最佳的 SDK 和连接
func (ju *UserUsecase) selectBestConnection(device *data.DeviceInfo) (string, *data.ConnInfo) {
	if len(device.SdkConn) == 0 {
		return "", nil
	}

	// 选择连接数最少的 SDK
	var bestSdkID string
	var bestConnInfo *data.ConnInfo
	minConnections := int(^uint(0) >> 1) // 最大整数

	for sdkID, connInfos := range device.SdkConn {
		if len(connInfos) > 0 && len(connInfos) < minConnections {
			minConnections = len(connInfos)
			bestSdkID = sdkID
			bestConnInfo = connInfos[0] // 选择第一个连接
		}
	}

	return bestSdkID, bestConnInfo
}

// getTraceIDFromContext 从上下文中获取trace_id
func (ju *UserUsecase) getTraceIDFromContext(ctx context.Context) string {
	if traceID, ok := ctx.Value("trace_id").(string); ok {
		return traceID
	}
	return ""
}

// getRequestIDFromContext 从上下文中获取request_id
func (ju *UserUsecase) getRequestIDFromContext(ctx context.Context) string {
	if requestID, ok := ctx.Value("request_id").(string); ok {
		return requestID
	}
	return ""
}
