package data

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/redis/go-redis/v9"
)

// LocationLevel 位置层级
type LocationLevel int

const (
	LocationLevelCountry LocationLevel = iota // 国家级 (US)
	LocationLevelState                        // 州级 (US_California)
	LocationLevelCity                         // 城市级 (US_California_LosAngeles)
)

// LocationIndexManager 位置索引管理器
type LocationIndexManager struct {
	rdb *redis.Client
}

func NewLocationIndexManager(rdb *redis.Client) *LocationIndexManager {
	return &LocationIndexManager{rdb: rdb}
}

// GetLocationIndexKey 获取位置索引键
func (m *LocationIndexManager) GetLocationIndexKey(supplier string, level LocationLevel, location string) string {
	levelStr := []string{"country", "state", "city"}[level]
	return fmt.Sprintf("location_index:%s:%s:%s", supplier, levelStr, location)
}

// GetLocationWeightKey 获取位置权重键
func (m *LocationIndexManager) GetLocationWeightKey(supplier string, level LocationLevel, location string) string {
	levelStr := []string{"country", "state", "city"}[level]
	return fmt.Sprintf("location_weight:%s:%s:%s", supplier, levelStr, location)
}

// ParseLocation 解析位置字符串
func (m *LocationIndexManager) ParseLocation(location string) (country, state, city string) {
	parts := strings.Split(location, "_")
	if len(parts) >= 1 {
		country = parts[0]
	}
	if len(parts) >= 2 {
		state = strings.Join(parts[:2], "_")
	}
	if len(parts) >= 3 {
		city = strings.Join(parts[:3], "_")
	}
	return
}

// AddDeviceToLocationIndex 添加设备到位置索引
func (m *LocationIndexManager) AddDeviceToLocationIndex(ctx context.Context, pipe redis.Pipeliner, supplier, location, ip string) {
	country, state, city := m.ParseLocation(location)

	// 添加到各级索引
	if country != "" {
		countryIndexKey := m.GetLocationIndexKey(supplier, LocationLevelCountry, country)
		countryWeightKey := m.GetLocationWeightKey(supplier, LocationLevelCountry, country)
		pipe.SAdd(ctx, countryIndexKey, ip)
		pipe.ZIncrBy(ctx, countryWeightKey, 1, location) // 增加完整位置的权重
		pipe.Expire(ctx, countryIndexKey, 24*time.Hour)
		pipe.Expire(ctx, countryWeightKey, 24*time.Hour)
	}

	if state != "" {
		stateIndexKey := m.GetLocationIndexKey(supplier, LocationLevelState, state)
		stateWeightKey := m.GetLocationWeightKey(supplier, LocationLevelState, state)
		pipe.SAdd(ctx, stateIndexKey, ip)
		pipe.ZIncrBy(ctx, stateWeightKey, 1, location)
		pipe.Expire(ctx, stateIndexKey, 24*time.Hour)
		pipe.Expire(ctx, stateWeightKey, 24*time.Hour)
	}

	if city != "" {
		cityIndexKey := m.GetLocationIndexKey(supplier, LocationLevelCity, city)
		cityWeightKey := m.GetLocationWeightKey(supplier, LocationLevelCity, city)
		pipe.SAdd(ctx, cityIndexKey, ip)
		pipe.ZIncrBy(ctx, cityWeightKey, 1, location)
		pipe.Expire(ctx, cityIndexKey, 24*time.Hour)
		pipe.Expire(ctx, cityWeightKey, 24*time.Hour)
	}
}

// RemoveDeviceFromLocationIndex 从位置索引中移除设备
func (m *LocationIndexManager) RemoveDeviceFromLocationIndex(ctx context.Context, pipe redis.Pipeliner, supplier, location, ip string) {
	country, state, city := m.ParseLocation(location)

	// 从各级索引中移除
	if country != "" {
		countryIndexKey := m.GetLocationIndexKey(supplier, LocationLevelCountry, country)
		countryWeightKey := m.GetLocationWeightKey(supplier, LocationLevelCountry, country)
		pipe.SRem(ctx, countryIndexKey, ip)
		pipe.ZIncrBy(ctx, countryWeightKey, -1, location)
		pipe.ZRemRangeByScore(ctx, countryWeightKey, "-inf", "0") // 清理零权重
	}

	if state != "" {
		stateIndexKey := m.GetLocationIndexKey(supplier, LocationLevelState, state)
		stateWeightKey := m.GetLocationWeightKey(supplier, LocationLevelState, state)
		pipe.SRem(ctx, stateIndexKey, ip)
		pipe.ZIncrBy(ctx, stateWeightKey, -1, location)
		pipe.ZRemRangeByScore(ctx, stateWeightKey, "-inf", "0")
	}

	if city != "" {
		cityIndexKey := m.GetLocationIndexKey(supplier, LocationLevelCity, city)
		cityWeightKey := m.GetLocationWeightKey(supplier, LocationLevelCity, city)
		pipe.SRem(ctx, cityIndexKey, ip)
		pipe.ZIncrBy(ctx, cityWeightKey, -1, location)
		pipe.ZRemRangeByScore(ctx, cityWeightKey, "-inf", "0")
	}
}

// GetRandomIPByLocationLevel 根据位置层级随机获取IP
func (m *LocationIndexManager) GetRandomIPByLocationLevel(ctx context.Context, supplier string, level LocationLevel, location string) (string, error) {
	indexKey := m.GetLocationIndexKey(supplier, level, location)

	// 使用 SRANDMEMBER 随机获取一个IP
	ip, err := m.rdb.SRandMember(ctx, indexKey).Result()
	if err != nil {
		if err == redis.Nil {
			return "", nil
		}
		return "", err
	}

	return ip, nil
}

// GetRandomIPByLocationLevelWeighted 根据位置层级和权重随机获取IP
func (m *LocationIndexManager) GetRandomIPByLocationLevelWeighted(ctx context.Context, supplier string, level LocationLevel, location string) (string, error) {
	weightKey := m.GetLocationWeightKey(supplier, level, location)

	// 1. 基于权重随机选择具体位置
	result, err := m.rdb.ZRandMemberWithScores(ctx, weightKey, 1).Result()
	if err != nil {
		if err == redis.Nil {
			return "", nil
		}
		return "", err
	}

	if len(result) == 0 {
		return "", nil
	}

	selectedLocation := result[0].Member.(string)

	// 2. 从选中的具体位置随机获取IP
	locationKey := GetLocationKey(supplier, selectedLocation)
	ip, err := m.rdb.SRandMember(ctx, locationKey).Result()
	if err != nil {
		if err == redis.Nil {
			return "", nil
		}
		return "", err
	}

	return ip, nil
}

// GetIPCountByLocationLevel 获取位置层级的IP数量
func (m *LocationIndexManager) GetIPCountByLocationLevel(ctx context.Context, supplier string, level LocationLevel, location string) (int64, error) {
	indexKey := m.GetLocationIndexKey(supplier, level, location)
	return m.rdb.SCard(ctx, indexKey).Result()
}

// GetLocationsByLevel 获取某层级下的所有位置
func (m *LocationIndexManager) GetLocationsByLevel(ctx context.Context, supplier string, level LocationLevel, location string) ([]string, error) {
	weightKey := m.GetLocationWeightKey(supplier, level, location)

	// 获取所有有权重的位置
	result, err := m.rdb.ZRangeWithScores(ctx, weightKey, 0, -1).Result()
	if err != nil {
		return nil, err
	}

	locations := make([]string, 0, len(result))
	for _, z := range result {
		if z.Score > 0 {
			locations = append(locations, z.Member.(string))
		}
	}

	return locations, nil
}

// LocationQueryStrategy 位置查询策略
type LocationQueryStrategy struct {
	indexManager *LocationIndexManager
}

func NewLocationQueryStrategy(rdb *redis.Client) *LocationQueryStrategy {
	return &LocationQueryStrategy{
		indexManager: NewLocationIndexManager(rdb),
	}
}

// GetRandomIPByLocation 根据位置获取随机IP（支持一级、二级查询）
func (s *LocationQueryStrategy) GetRandomIPByLocation(ctx context.Context, supplier, location string) (string, error) {
	// 解析位置层级
	parts := strings.Split(location, "_")

	var level LocationLevel
	var queryLocation string

	switch len(parts) {
	case 1:
		// 一级查询：国家级 (US)
		level = LocationLevelCountry
		queryLocation = parts[0]
	case 2:
		// 二级查询：州级 (US_California)
		level = LocationLevelState
		queryLocation = location
	case 3:
		// 三级查询：城市级 (US_California_LosAngeles)
		level = LocationLevelCity
		queryLocation = location
	default:
		return "", fmt.Errorf("invalid location format: %s", location)
	}

	// 使用加权随机选择
	return s.indexManager.GetRandomIPByLocationLevelWeighted(ctx, supplier, level, queryLocation)
}

// GetRandomIPByLocationDirect 直接从位置索引获取随机IP（更快）
func (s *LocationQueryStrategy) GetRandomIPByLocationDirect(ctx context.Context, supplier, location string) (string, error) {
	parts := strings.Split(location, "_")

	var level LocationLevel
	var queryLocation string

	switch len(parts) {
	case 1:
		level = LocationLevelCountry
		queryLocation = parts[0]
	case 2:
		level = LocationLevelState
		queryLocation = location
	case 3:
		level = LocationLevelCity
		queryLocation = location
	default:
		return "", fmt.Errorf("invalid location format: %s", location)
	}

	// 直接从索引获取随机IP（不考虑权重，性能最优）
	return s.indexManager.GetRandomIPByLocationLevel(ctx, supplier, level, queryLocation)
}

// BatchGetRandomIPs 批量获取随机IP
func (s *LocationQueryStrategy) BatchGetRandomIPs(ctx context.Context, supplier, location string, count int) ([]string, error) {
	parts := strings.Split(location, "_")

	var level LocationLevel
	var queryLocation string

	switch len(parts) {
	case 1:
		level = LocationLevelCountry
		queryLocation = parts[0]
	case 2:
		level = LocationLevelState
		queryLocation = location
	case 3:
		level = LocationLevelCity
		queryLocation = location
	default:
		return nil, fmt.Errorf("invalid location format: %s", location)
	}

	indexKey := s.indexManager.GetLocationIndexKey(supplier, level, queryLocation)

	// 使用 SRANDMEMBER 批量获取
	ips, err := s.indexManager.rdb.SRandMemberN(ctx, indexKey, int64(count)).Result()
	if err != nil {
		return nil, err
	}

	return ips, nil
}
