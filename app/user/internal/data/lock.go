package data

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"errors"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
)

var (
	// ErrLockNotAcquired 获取锁失败
	ErrLockNotAcquired = errors.New("failed to acquire lock")
	// ErrLockNotHeld 锁不存在或已被其他进程持有
	ErrLockNotHeld = errors.New("lock not held")
	// ErrLockExpired 锁已过期
	ErrLockExpired = errors.New("lock expired")
)

// DistributedLock Redis分布式锁接口
type DistributedLock interface {
	// Lock 获取锁
	Lock(ctx context.Context, key string, ttl time.Duration) (*LockHandle, error)
	// TryLock 尝试获取锁，不阻塞
	TryLock(ctx context.Context, key string, ttl time.Duration) (*LockHandle, error)
	// LockWithRetry 带重试的获取锁
	LockWithRetry(ctx context.Context, key string, ttl time.Duration, maxRetries int, retryInterval time.Duration) (*LockHandle, error)
}

// LockHandle 锁句柄
type LockHandle struct {
	key       string
	value     string
	ttl       time.Duration
	redis     *redis.Client
	ctx       context.Context
	cancelCtx context.CancelFunc
}

// RedisDistributedLock Redis分布式锁实现
type RedisDistributedLock struct {
	redis *redis.Client
}

// NewRedisDistributedLock 创建Redis分布式锁
func NewRedisDistributedLock(rdb *redis.Client) DistributedLock {
	return &RedisDistributedLock{
		redis: rdb,
	}
}

// generateLockValue 生成锁的唯一值
func generateLockValue() string {
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		// 如果随机数生成失败，使用时间戳作为备选方案
		return fmt.Sprintf("lock_%d", time.Now().UnixNano())
	}
	return hex.EncodeToString(bytes)
}

// Lock 获取锁（阻塞直到获取成功或超时）
func (l *RedisDistributedLock) Lock(ctx context.Context, key string, ttl time.Duration) (*LockHandle, error) {
	return l.LockWithRetry(ctx, key, ttl, -1, 100*time.Millisecond)
}

// TryLock 尝试获取锁（不阻塞）
func (l *RedisDistributedLock) TryLock(ctx context.Context, key string, ttl time.Duration) (*LockHandle, error) {
	value := generateLockValue()
	lockKey := fmt.Sprintf("lock:%s", key)

	// 使用 SET NX EX 命令原子性地设置锁
	result, err := l.redis.SetNX(ctx, lockKey, value, ttl).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to acquire lock: %w", err)
	}

	if !result {
		return nil, ErrLockNotAcquired
	}

	// 创建锁句柄
	lockCtx, cancel := context.WithCancel(context.Background())
	handle := &LockHandle{
		key:       lockKey,
		value:     value,
		ttl:       ttl,
		redis:     l.redis,
		ctx:       lockCtx,
		cancelCtx: cancel,
	}

	// 启动自动续期
	go handle.autoRenew()

	return handle, nil
}

// LockWithRetry 带重试的获取锁
func (l *RedisDistributedLock) LockWithRetry(ctx context.Context, key string, ttl time.Duration, maxRetries int, retryInterval time.Duration) (*LockHandle, error) {
	var lastErr error
	retries := 0

	for {
		handle, err := l.TryLock(ctx, key, ttl)
		if err == nil {
			return handle, nil
		}

		lastErr = err

		// 如果不是获取锁失败的错误，直接返回
		if !errors.Is(err, ErrLockNotAcquired) {
			return nil, err
		}

		// 检查是否达到最大重试次数
		if maxRetries >= 0 && retries >= maxRetries {
			break
		}

		retries++

		// 等待重试间隔
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		case <-time.After(retryInterval):
			// 继续重试
		}
	}

	return nil, fmt.Errorf("failed to acquire lock after %d retries: %w", retries, lastErr)
}

// Unlock 释放锁
func (h *LockHandle) Unlock() error {
	// 停止自动续期
	h.cancelCtx()

	// 使用 Lua 脚本确保只有锁的持有者才能释放锁
	luaScript := `
		if redis.call("GET", KEYS[1]) == ARGV[1] then
			return redis.call("DEL", KEYS[1])
		else
			return 0
		end
	`

	result, err := h.redis.Eval(context.Background(), luaScript, []string{h.key}, h.value).Result()
	if err != nil {
		return fmt.Errorf("failed to release lock: %w", err)
	}

	if result.(int64) == 0 {
		return ErrLockNotHeld
	}

	return nil
}

// Extend 延长锁的过期时间
func (h *LockHandle) Extend(ttl time.Duration) error {
	// 使用 Lua 脚本确保只有锁的持有者才能延长锁
	luaScript := `
		if redis.call("GET", KEYS[1]) == ARGV[1] then
			return redis.call("EXPIRE", KEYS[1], ARGV[2])
		else
			return 0
		end
	`

	result, err := h.redis.Eval(context.Background(), luaScript, []string{h.key}, h.value, int(ttl.Seconds())).Result()
	if err != nil {
		return fmt.Errorf("failed to extend lock: %w", err)
	}

	if result.(int64) == 0 {
		return ErrLockNotHeld
	}

	h.ttl = ttl
	return nil
}

// IsHeld 检查锁是否仍然被持有
func (h *LockHandle) IsHeld() bool {
	value, err := h.redis.Get(context.Background(), h.key).Result()
	if err != nil {
		return false
	}
	return value == h.value
}

// TTL 获取锁的剩余过期时间
func (h *LockHandle) TTL() (time.Duration, error) {
	ttl, err := h.redis.TTL(context.Background(), h.key).Result()
	if err != nil {
		return 0, err
	}

	if ttl == -2 {
		return 0, ErrLockNotHeld
	}

	if ttl == -1 {
		return 0, ErrLockExpired
	}

	return ttl, nil
}

// autoRenew 自动续期锁
func (h *LockHandle) autoRenew() {
	// 在锁过期前的1/3时间开始续期
	renewInterval := h.ttl / 3
	if renewInterval < time.Second {
		renewInterval = time.Second
	}

	ticker := time.NewTicker(renewInterval)
	defer ticker.Stop()

	for {
		select {
		case <-h.ctx.Done():
			return
		case <-ticker.C:
			// 续期锁
			if err := h.Extend(h.ttl); err != nil {
				// 续期失败，可能锁已经被释放或过期
				return
			}
		}
	}
}

// LockManager 锁管理器，提供高级锁操作
type LockManager struct {
	lock DistributedLock
}

// NewLockManager 创建锁管理器
func NewLockManager(rdb *redis.Client) *LockManager {
	return &LockManager{
		lock: NewRedisDistributedLock(rdb),
	}
}

// WithLock 使用锁执行函数
func (m *LockManager) WithLock(ctx context.Context, key string, ttl time.Duration, fn func() error) error {
	handle, err := m.lock.Lock(ctx, key, ttl)
	if err != nil {
		return err
	}
	defer handle.Unlock()

	return fn()
}

// WithTryLock 尝试使用锁执行函数
func (m *LockManager) WithTryLock(ctx context.Context, key string, ttl time.Duration, fn func() error) error {
	handle, err := m.lock.TryLock(ctx, key, ttl)
	if err != nil {
		return err
	}
	defer handle.Unlock()

	return fn()
}

// WithLockRetry 带重试的使用锁执行函数
func (m *LockManager) WithLockRetry(ctx context.Context, key string, ttl time.Duration, maxRetries int, retryInterval time.Duration, fn func() error) error {
	handle, err := m.lock.LockWithRetry(ctx, key, ttl, maxRetries, retryInterval)
	if err != nil {
		return err
	}
	defer handle.Unlock()

	return fn()
}

// 预定义的锁键生成函数

// GetDeviceLockKey 获取设备锁键
func GetDeviceLockKey(supplier, sdkIP string) string {
	return fmt.Sprintf("lock:device:%s:%s", supplier, sdkIP)
}

// GetIPInfoLockKey 获取IP信息锁键
func GetIPInfoLockKey(ip string) string {
	return fmt.Sprintf("lock:ipinfo:%s", ip)
}

// GetLocationLockKey 获取位置锁键
func GetLocationLockKey(supplier, location string) string {
	return fmt.Sprintf("lock:location:%s:%s", supplier, location)
}

// GetSessionLockKey 获取会话锁键
func GetSessionLockKey(authUser, session string) string {
	return fmt.Sprintf("lock:session:%s:%s", authUser, session)
}
