package data

import (
	"context"
	"fmt"
	"sync"
	"testing"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// 创建测试用的Redis客户端
func createTestRedisClient() *redis.Client {
	return redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
		DB:   1, // 使用测试数据库
	})
}

func TestRedisDistributedLock_TryLock(t *testing.T) {
	rdb := createTestRedisClient()
	defer rdb.Close()

	// 清理测试数据
	rdb.FlushDB(context.Background())

	lock := NewRedisDistributedLock(rdb)
	ctx := context.Background()

	// 测试获取锁
	handle, err := lock.TryLock(ctx, "test_key", 5*time.Second)
	require.NoError(t, err)
	require.NotNil(t, handle)

	// 验证锁被持有
	assert.True(t, handle.IsHeld())

	// 测试重复获取同一个锁应该失败
	handle2, err := lock.TryLock(ctx, "test_key", 5*time.Second)
	assert.Error(t, err)
	assert.Equal(t, ErrLockNotAcquired, err)
	assert.Nil(t, handle2)

	// 释放锁
	err = handle.Unlock()
	assert.NoError(t, err)

	// 验证锁已释放
	assert.False(t, handle.IsHeld())

	// 现在应该能够重新获取锁
	handle3, err := lock.TryLock(ctx, "test_key", 5*time.Second)
	assert.NoError(t, err)
	assert.NotNil(t, handle3)

	handle3.Unlock()
}

func TestRedisDistributedLock_LockWithRetry(t *testing.T) {
	rdb := createTestRedisClient()
	defer rdb.Close()

	// 清理测试数据
	rdb.FlushDB(context.Background())

	lock := NewRedisDistributedLock(rdb)
	ctx := context.Background()

	// 先获取一个锁
	handle1, err := lock.TryLock(ctx, "test_key", 2*time.Second)
	require.NoError(t, err)

	// 在另一个goroutine中延迟释放锁
	go func() {
		time.Sleep(1 * time.Second)
		handle1.Unlock()
	}()

	// 使用重试机制获取锁
	start := time.Now()
	handle2, err := lock.LockWithRetry(ctx, "test_key", 5*time.Second, 10, 200*time.Millisecond)
	duration := time.Since(start)

	assert.NoError(t, err)
	assert.NotNil(t, handle2)
	assert.True(t, duration >= 1*time.Second) // 应该等待至少1秒
	assert.True(t, duration < 2*time.Second)  // 但不应该等待太久

	handle2.Unlock()
}

func TestRedisDistributedLock_AutoRenew(t *testing.T) {
	rdb := createTestRedisClient()
	defer rdb.Close()

	// 清理测试数据
	rdb.FlushDB(context.Background())

	lock := NewRedisDistributedLock(rdb)
	ctx := context.Background()

	// 获取一个短TTL的锁
	handle, err := lock.TryLock(ctx, "test_key", 2*time.Second)
	require.NoError(t, err)

	// 等待超过原始TTL时间
	time.Sleep(3 * time.Second)

	// 锁应该仍然被持有（因为自动续期）
	assert.True(t, handle.IsHeld())

	// 手动延长锁
	err = handle.Extend(10 * time.Second)
	assert.NoError(t, err)

	// 检查TTL
	ttl, err := handle.TTL()
	assert.NoError(t, err)
	assert.True(t, ttl > 5*time.Second)

	handle.Unlock()
}

func TestRedisDistributedLock_Concurrent(t *testing.T) {
	rdb := createTestRedisClient()
	defer rdb.Close()

	// 清理测试数据
	rdb.FlushDB(context.Background())

	lock := NewRedisDistributedLock(rdb)
	ctx := context.Background()

	const numGoroutines = 10
	const lockKey = "concurrent_test"

	var counter int
	var mu sync.Mutex
	var wg sync.WaitGroup

	// 启动多个goroutine并发获取锁
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()

			handle, err := lock.LockWithRetry(ctx, lockKey, 5*time.Second, 20, 100*time.Millisecond)
			if err != nil {
				t.Errorf("Goroutine %d failed to acquire lock: %v", id, err)
				return
			}

			// 临界区：增加计数器
			mu.Lock()
			oldCounter := counter
			time.Sleep(10 * time.Millisecond) // 模拟一些工作
			counter = oldCounter + 1
			mu.Unlock()

			handle.Unlock()
		}(i)
	}

	wg.Wait()

	// 验证计数器的值
	assert.Equal(t, numGoroutines, counter)
}

func TestLockManager_WithLock(t *testing.T) {
	rdb := createTestRedisClient()
	defer rdb.Close()

	// 清理测试数据
	rdb.FlushDB(context.Background())

	manager := NewLockManager(rdb)
	ctx := context.Background()

	executed := false

	err := manager.WithLock(ctx, "test_key", 5*time.Second, func() error {
		executed = true
		return nil
	})

	assert.NoError(t, err)
	assert.True(t, executed)
}

func TestLockManager_WithTryLock(t *testing.T) {
	rdb := createTestRedisClient()
	defer rdb.Close()

	// 清理测试数据
	rdb.FlushDB(context.Background())

	manager := NewLockManager(rdb)
	ctx := context.Background()

	// 先获取锁
	lock := NewRedisDistributedLock(rdb)
	handle, err := lock.TryLock(ctx, "test_key", 5*time.Second)
	require.NoError(t, err)

	// 尝试使用WithTryLock应该失败
	executed := false
	err = manager.WithTryLock(ctx, "test_key", 5*time.Second, func() error {
		executed = true
		return nil
	})

	assert.Error(t, err)
	assert.Equal(t, ErrLockNotAcquired, err)
	assert.False(t, executed)

	// 释放锁后应该成功
	handle.Unlock()

	err = manager.WithTryLock(ctx, "test_key", 5*time.Second, func() error {
		executed = true
		return nil
	})

	assert.NoError(t, err)
	assert.True(t, executed)
}

func TestLockKeyGenerators(t *testing.T) {
	// 测试锁键生成函数
	assert.Equal(t, "device:sdk001:***********", GetDeviceLockKey("sdk001", "***********"))
	assert.Equal(t, "ipinfo:***********", GetIPInfoLockKey("***********"))
	assert.Equal(t, "location:US_CA_LA", GetLocationLockKey("US_CA_LA"))
	assert.Equal(t, "session:user123:sess456", GetSessionLockKey("user123", "sess456"))
}

// 基准测试
func BenchmarkRedisDistributedLock_TryLock(b *testing.B) {
	rdb := createTestRedisClient()
	defer rdb.Close()

	lock := NewRedisDistributedLock(rdb)
	ctx := context.Background()

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		key := fmt.Sprintf("bench_key_%d", i)
		handle, err := lock.TryLock(ctx, key, 5*time.Second)
		if err != nil {
			b.Fatalf("Failed to acquire lock: %v", err)
		}
		handle.Unlock()
	}
}

func BenchmarkLockManager_WithLock(b *testing.B) {
	rdb := createTestRedisClient()
	defer rdb.Close()

	manager := NewLockManager(rdb)
	ctx := context.Background()

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		key := fmt.Sprintf("bench_key_%d", i)
		err := manager.WithLock(ctx, key, 5*time.Second, func() error {
			// 模拟一些工作
			return nil
		})
		if err != nil {
			b.Fatalf("Failed to execute with lock: %v", err)
		}
	}
}
