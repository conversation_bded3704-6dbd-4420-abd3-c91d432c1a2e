package log

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"
)

// Helper 扩展的日志助手，支持自动从 context 注入字段
type Helper struct {
	*log.Helper
	logger log.Logger
}

// NewHelper 创建扩展的日志助手
func NewHelper(logger log.Logger, keyvals ...any) *Helper {
	return &Helper{
		Helper: log.NewHelper(log.With(logger, keyvals...)),
		logger: logger,
	}
}

// WithContext 创建带有 context 的日志方法
func (h *Helper) WithContext(ctx context.Context) *ContextMethods {
	return &ContextMethods{
		ctx:    ctx,
		helper: h.Helper,
		logger: h.logger,
	}
}

// ContextMethods 带有 context 的日志方法
type ContextMethods struct {
	ctx    context.Context
	helper *log.Helper
	logger log.Logger
}

// extractContextFields 从 context 中提取字段
func (c *ContextMethods) extractContextFields() []any {
	var fields []any

	// 提取 trace_id
	if traceID, ok := c.ctx.Value("trace_id").(string); ok && traceID != "" {
		fields = append(fields, "trace_id", traceID)
	}

	// 提取 request_id
	if requestID, ok := c.ctx.Value("request_id").(string); ok && requestID != "" {
		fields = append(fields, "request_id", requestID)
	}

	// 提取 user_id
	if userID, ok := c.ctx.Value("user_id").(string); ok && userID != "" {
		fields = append(fields, "user_id", userID)
	}

	// 提取 session_id
	if sessionID, ok := c.ctx.Value("session_id").(string); ok && sessionID != "" {
		fields = append(fields, "session_id", sessionID)
	}

	return fields
}

// mergeFields 合并 context 字段和用户提供的字段
func (c *ContextMethods) mergeFields(keyvals ...any) []any {
	contextFields := c.extractContextFields()
	return append(contextFields, keyvals...)
}

// Debug 记录 Debug 级别日志
func (c *ContextMethods) Debug(a ...any) {
	c.helper.Debug(a...)
}

// Debugf 记录格式化的 Debug 级别日志
func (c *ContextMethods) Debugf(format string, a ...any) {
	c.helper.Debugf(format, a...)
}

// Debugw 记录带字段的 Debug 级别日志，自动注入 context 字段
func (c *ContextMethods) Debugw(keyvals ...any) {
	mergedFields := c.mergeFields(keyvals...)
	c.helper.Debugw(mergedFields...)
}

// Info 记录 Info 级别日志
func (c *ContextMethods) Info(a ...any) {
	c.helper.Info(a...)
}

// Infof 记录格式化的 Info 级别日志
func (c *ContextMethods) Infof(format string, a ...any) {
	c.helper.Infof(format, a...)
}

// Infow 记录带字段的 Info 级别日志，自动注入 context 字段
func (c *ContextMethods) Infow(keyvals ...any) {
	mergedFields := c.mergeFields(keyvals...)
	c.helper.Infow(mergedFields...)
}

// Warn 记录 Warn 级别日志
func (c *ContextMethods) Warn(a ...any) {
	c.helper.Warn(a...)
}

// Warnf 记录格式化的 Warn 级别日志
func (c *ContextMethods) Warnf(format string, a ...any) {
	c.helper.Warnf(format, a...)
}

// Warnw 记录带字段的 Warn 级别日志，自动注入 context 字段
func (c *ContextMethods) Warnw(keyvals ...any) {
	mergedFields := c.mergeFields(keyvals...)
	c.helper.Warnw(mergedFields...)
}

// Error 记录 Error 级别日志
func (c *ContextMethods) Error(a ...any) {
	c.helper.Error(a...)
}

// Errorf 记录格式化的 Error 级别日志
func (c *ContextMethods) Errorf(format string, a ...any) {
	c.helper.Errorf(format, a...)
}

// Errorw 记录带字段的 Error 级别日志，自动注入 context 字段
func (c *ContextMethods) Errorw(keyvals ...any) {
	mergedFields := c.mergeFields(keyvals...)
	c.helper.Errorw(mergedFields...)
}

// Fatal 记录 Fatal 级别日志
func (c *ContextMethods) Fatal(a ...any) {
	c.helper.Fatal(a...)
}

// Fatalf 记录格式化的 Fatal 级别日志
func (c *ContextMethods) Fatalf(format string, a ...any) {
	c.helper.Fatalf(format, a...)
}

// Fatalw 记录带字段的 Fatal 级别日志，自动注入 context 字段
func (c *ContextMethods) Fatalw(keyvals ...any) {
	mergedFields := c.mergeFields(keyvals...)
	c.helper.Fatalw(mergedFields...)
}

// Log 记录日志，自动注入 context 字段
func (c *ContextMethods) Log(level log.Level, keyvals ...any) error {
	mergedFields := c.mergeFields(keyvals...)
	c.helper.Log(level, mergedFields...)
	return nil
}

// 便捷方法：直接使用 context 记录日志

// DebugContext 使用 context 记录 Debug 日志
func DebugContext(ctx context.Context, logger log.Logger, keyvals ...any) {
	helper := NewHelper(logger)
	helper.WithContext(ctx).Debugw(keyvals...)
}

// InfoContext 使用 context 记录 Info 日志
func InfoContext(ctx context.Context, logger log.Logger, keyvals ...any) {
	helper := NewHelper(logger)
	helper.WithContext(ctx).Infow(keyvals...)
}

// WarnContext 使用 context 记录 Warn 日志
func WarnContext(ctx context.Context, logger log.Logger, keyvals ...any) {
	helper := NewHelper(logger)
	helper.WithContext(ctx).Warnw(keyvals...)
}

// ErrorContext 使用 context 记录 Error 日志
func ErrorContext(ctx context.Context, logger log.Logger, keyvals ...any) {
	helper := NewHelper(logger)
	helper.WithContext(ctx).Errorw(keyvals...)
}

// FatalContext 使用 context 记录 Fatal 日志
func FatalContext(ctx context.Context, logger log.Logger, keyvals ...any) {
	helper := NewHelper(logger)
	helper.WithContext(ctx).Fatalw(keyvals...)
}

// LogContext 使用 context 记录指定级别的日志
func LogContext(ctx context.Context, logger log.Logger, level log.Level, keyvals ...any) error {
	helper := NewHelper(logger)
	return helper.WithContext(ctx).Log(level, keyvals...)
}
