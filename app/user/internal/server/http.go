package server

import (
	_ "net/http/pprof"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/metrics"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/go-kratos/kratos/v2/transport/http/pprof"
	"github.com/prometheus/client_golang/prometheus/promhttp"

	v1 "sh_proxy/api/user/v1"
	"sh_proxy/app/user/internal/conf"
	"sh_proxy/app/user/internal/service"
)

// NewHTTPServer new an HTTP server.
func NewHTTPServer(c *conf.Server, user *service.UserService, logger log.Logger) *http.Server {
	var opts = []http.ServerOption{
		http.Middleware(
			recovery.Recovery(),
			// SecurityMiddleware(logger),
			RequestLogMiddleware(logger),
			UserServiceMiddleware(logger),
			PerformanceMiddleware(logger),
			// RateLimitMiddleware(logger),
			metrics.Server(
				metrics.WithSeconds(metricSeconds),
				metrics.WithRequests(metricRequests),
			),
		),
	}
	if c.Http.Network != "" {
		opts = append(opts, http.Network(c.Http.Network))
	}
	if c.Http.Addr != "" {
		opts = append(opts, http.Address(c.Http.Addr))
	}
	if c.Http.Timeout != nil {
		opts = append(opts, http.Timeout(c.Http.Timeout.AsDuration()))
	}
	srv := http.NewServer(opts...)
	srv.Handle("/metrics", promhttp.Handler())
	srv.Handle("debug/pprof", pprof.NewHandler())
	v1.RegisterUserHTTPServer(srv, user)

	return srv
}
