package server

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/transport"
	"github.com/go-kratos/kratos/v2/transport/http"
)

// RequestLogMiddleware 请求日志中间件
func RequestLogMiddleware(logger log.Logger) middleware.Middleware {
	return middleware.Chain(
		TraceIDMiddleware(),
		RequestIDMiddleware(),
		LoggingMiddleware(logger),
	)
}

// LoggingMiddleware 日志记录中间件
func LoggingMiddleware(logger log.Logger) middleware.Middleware {
	log := log.NewHelper(log.With(logger, "module", "middleware/logging"))

	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req any) (any, error) {
			var (
				code      int32
				reason    string
				kind      string
				operation string
			)

			startTime := time.Now()

			// 从上下文中获取trace_id和request_id
			traceID := getTraceIDFromContext(ctx)
			requestID := getRequestIDFromContext(ctx)

			if info, ok := transport.FromServerContext(ctx); ok {
				kind = info.Kind().String()
				operation = info.Operation()
			}

			// 记录请求开始
			log.Infow(
				"msg", "request started",
				"trace_id", traceID,
				"request_id", requestID,
				"kind", kind,
				"operation", operation,
				"request", formatRequest(req),
				"start_time", startTime.Format(time.RFC3339),
			)

			reply, err := handler(ctx, req)

			// 计算耗时
			duration := time.Since(startTime)

			if err != nil {
				// 如果有错误，尝试提取错误码和原因
				if se, ok := err.(interface {
					GRPCStatus() interface {
						Code() int32
						Message() string
					}
				}); ok {
					status := se.GRPCStatus()
					code = status.Code()
					reason = status.Message()
				} else {
					code = 500
					reason = err.Error()
				}

				// 记录错误请求
				log.Errorw(
					"msg", "request failed",
					"trace_id", traceID,
					"request_id", requestID,
					"kind", kind,
					"operation", operation,
					"request", formatRequest(req),
					"error", err.Error(),
					"code", code,
					"reason", reason,
					"duration", duration.String(),
					"duration_ms", duration.Milliseconds(),
				)
			} else {
				// 记录成功请求
				log.Infow(
					"msg", "request completed",
					"trace_id", traceID,
					"request_id", requestID,
					"kind", kind,
					"operation", operation,
					"request", formatRequest(req),
					"response", formatResponse(reply),
					"code", 0,
					"duration", duration.String(),
					"duration_ms", duration.Milliseconds(),
				)
			}

			return reply, err
		}
	}
}

// TraceIDMiddleware Trace ID中间件
func TraceIDMiddleware() middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			var traceID string

			// 尝试从HTTP Header中获取trace_id
			if info, ok := transport.FromServerContext(ctx); ok {
				if httpInfo, ok := info.(*http.Transport); ok {
					traceID = httpInfo.Request().Header.Get("X-Trace-ID")
					if traceID == "" {
						traceID = httpInfo.Request().Header.Get("Trace-ID")
					}
					if traceID == "" {
						traceID = httpInfo.Request().Header.Get("X-Request-ID")
					}
				}
			}

			// 如果没有找到trace_id，生成一个新的
			if traceID == "" {
				traceID = generateTraceID()
			}

			// 将trace_id添加到上下文
			ctx = context.WithValue(ctx, "trace_id", traceID)

			// 如果是HTTP响应，设置trace_id到响应头
			if info, ok := transport.FromServerContext(ctx); ok {
				if httpInfo, ok := info.(*http.Transport); ok {
					httpInfo.ReplyHeader().Set("X-Trace-ID", traceID)
				}
			}

			return handler(ctx, req)
		}
	}
}

// RequestIDMiddleware 请求ID中间件
func RequestIDMiddleware() middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			// 生成请求ID
			requestID := generateRequestID()

			// 将请求ID添加到上下文
			ctx = context.WithValue(ctx, "request_id", requestID)

			// 如果是HTTP请求，可以从header中获取或设置请求ID
			if info, ok := transport.FromServerContext(ctx); ok {
				if httpInfo, ok := info.(*http.Transport); ok {
					// 设置请求ID到响应头
					httpInfo.ReplyHeader().Set("X-Request-ID", requestID)
				}
			}

			return handler(ctx, req)
		}
	}
}

// PerformanceMiddleware 性能监控中间件
func PerformanceMiddleware(logger log.Logger) middleware.Middleware {
	log := log.NewHelper(log.With(logger, "module", "middleware/performance"))

	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			startTime := time.Now()

			reply, err := handler(ctx, req)

			duration := time.Since(startTime)

			// 记录性能指标
			if info, ok := transport.FromServerContext(ctx); ok {
				operation := info.Operation()

				// 如果请求耗时超过阈值，记录警告
				if duration > 5*time.Second {
					log.Warnw(
						"msg", "slow request detected",
						"operation", operation,
						"duration", duration.String(),
						"duration_ms", duration.Milliseconds(),
					)
				}

				// 记录性能指标（可以发送到监控系统）
				log.Debugw(
					"msg", "performance metrics",
					"operation", operation,
					"duration_ms", duration.Milliseconds(),
					"success", err == nil,
				)
			}

			return reply, err
		}
	}
}

// RateLimitMiddleware 限流中间件（简单实现）
func RateLimitMiddleware(logger log.Logger) middleware.Middleware {
	log := log.NewHelper(log.With(logger, "module", "middleware/ratelimit"))

	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			// 这里可以实现限流逻辑
			// 例如：基于IP、用户ID、API等进行限流

			// 简单示例：记录请求频率
			if info, ok := transport.FromServerContext(ctx); ok {
				log.Debugw(
					"msg", "rate limit check",
					"operation", info.Operation(),
					"kind", info.Kind().String(),
				)
			}

			return handler(ctx, req)
		}
	}
}

// formatRequest 格式化请求参数用于日志记录
func formatRequest(req interface{}) string {
	if req == nil {
		return ""
	}

	// 将请求序列化为JSON，但限制长度避免日志过长
	data, err := json.Marshal(req)
	if err != nil {
		return fmt.Sprintf("marshal_error: %v", err)
	}

	str := string(data)
	if len(str) > 1000 {
		str = str[:1000] + "..."
	}

	return str
}

// formatResponse 格式化响应参数用于日志记录
func formatResponse(resp interface{}) string {
	if resp == nil {
		return ""
	}

	// 将响应序列化为JSON，但限制长度避免日志过长
	data, err := json.Marshal(resp)
	if err != nil {
		return fmt.Sprintf("marshal_error: %v", err)
	}

	str := string(data)
	if len(str) > 500 {
		str = str[:500] + "..."
	}

	return str
}

// generateTraceID 生成Trace ID
func generateTraceID() string {
	// 生成16字节的随机数作为trace_id
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		// 如果随机数生成失败，使用时间戳作为备选方案
		return fmt.Sprintf("trace_%d", time.Now().UnixNano())
	}
	return hex.EncodeToString(bytes)
}

// generateRequestID 生成请求ID
func generateRequestID() string {
	// 生成8字节的随机数作为request_id
	bytes := make([]byte, 8)
	if _, err := rand.Read(bytes); err != nil {
		// 如果随机数生成失败，使用时间戳作为备选方案
		return fmt.Sprintf("req_%d", time.Now().UnixNano())
	}
	return hex.EncodeToString(bytes)
}

// setTraceIDToContext 将trace_id设置到上下文
func setTraceIDToContext(ctx context.Context, traceID string) context.Context {
	return context.WithValue(ctx, "trace_id", traceID)
}

// setRequestIDToContext 将request_id设置到上下文
func setRequestIDToContext(ctx context.Context, requestID string) context.Context {
	return context.WithValue(ctx, "request_id", requestID)
}
