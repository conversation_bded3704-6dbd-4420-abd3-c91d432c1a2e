package server

import (
	"time"
)

// MiddlewareConfig 中间件配置
type MiddlewareConfig struct {
	// 日志配置
	Logging LoggingConfig `json:"logging"`

	// 性能监控配置
	Performance PerformanceConfig `json:"performance"`

	// 限流配置
	RateLimit RateLimitConfig `json:"rate_limit"`

	// 安全配置
	Security SecurityConfig `json:"security"`
}

// LoggingConfig 日志配置
type LoggingConfig struct {
	// 是否启用请求日志
	EnableRequestLog bool `json:"enable_request_log"`

	// 是否启用响应日志
	EnableResponseLog bool `json:"enable_response_log"`

	// 是否记录请求体
	LogRequestBody bool `json:"log_request_body"`

	// 是否记录响应体
	LogResponseBody bool `json:"log_response_body"`

	// 最大日志长度
	MaxLogLength int `json:"max_log_length"`

	// 敏感字段列表
	SensitiveFields []string `json:"sensitive_fields"`
}

// PerformanceConfig 性能监控配置
type PerformanceConfig struct {
	// 是否启用性能监控
	Enabled bool `json:"enabled"`

	// 慢请求阈值
	SlowRequestThreshold time.Duration `json:"slow_request_threshold"`

	// 是否记录所有请求的性能指标
	LogAllRequests bool `json:"log_all_requests"`
}

// RateLimitConfig 限流配置
type RateLimitConfig struct {
	// 是否启用限流
	Enabled bool `json:"enabled"`

	// 每秒请求数限制
	RequestsPerSecond int `json:"requests_per_second"`

	// 突发请求数限制
	BurstSize int `json:"burst_size"`

	// 限流窗口大小
	WindowSize time.Duration `json:"window_size"`
}

// SecurityConfig 安全配置
type SecurityConfig struct {
	// 是否启用安全检查
	Enabled bool `json:"enabled"`

	// IP白名单
	IPWhitelist []string `json:"ip_whitelist"`

	// 是否启用IP白名单检查
	EnableIPWhitelist bool `json:"enable_ip_whitelist"`

	// 最大请求体大小
	MaxRequestSize int64 `json:"max_request_size"`

	// 是否记录安全事件
	LogSecurityEvents bool `json:"log_security_events"`
}

// DefaultMiddlewareConfig 默认中间件配置
func DefaultMiddlewareConfig() *MiddlewareConfig {
	return &MiddlewareConfig{
		Logging: LoggingConfig{
			EnableRequestLog:  true,
			EnableResponseLog: true,
			LogRequestBody:    true,
			LogResponseBody:   true,
			MaxLogLength:      1000,
			SensitiveFields:   []string{"password", "token", "secret", "key"},
		},
		Performance: PerformanceConfig{
			Enabled:              true,
			SlowRequestThreshold: 5 * time.Second,
			LogAllRequests:       false,
		},
		RateLimit: RateLimitConfig{
			Enabled:           true,
			RequestsPerSecond: 1000,
			BurstSize:         100,
			WindowSize:        time.Minute,
		},
		Security: SecurityConfig{
			Enabled:           true,
			IPWhitelist:       []string{},
			EnableIPWhitelist: false,
			MaxRequestSize:    10 * 1024 * 1024, // 10MB
			LogSecurityEvents: true,
		},
	}
}

// UserServiceMetrics 用户服务指标
type UserServiceMetrics struct {
	// 连接事件计数
	ConnectEvents int64 `json:"connect_events"`

	// 断连事件计数
	DisconnectEvents int64 `json:"disconnect_events"`

	// 路由请求计数
	RouteRequests int64 `json:"route_requests"`

	// 错误计数
	ErrorCount int64 `json:"error_count"`

	// 平均响应时间
	AvgResponseTime time.Duration `json:"avg_response_time"`

	// 当前在线设备数
	OnlineDevices int64 `json:"online_devices"`

	// 最后更新时间
	LastUpdated time.Time `json:"last_updated"`
}

// EventMetrics 事件指标
type EventMetrics struct {
	// 事件类型
	EventType string `json:"event_type"`

	// 处理次数
	Count int64 `json:"count"`

	// 成功次数
	SuccessCount int64 `json:"success_count"`

	// 失败次数
	ErrorCount int64 `json:"error_count"`

	// 平均处理时间
	AvgDuration time.Duration `json:"avg_duration"`

	// 最大处理时间
	MaxDuration time.Duration `json:"max_duration"`

	// 最小处理时间
	MinDuration time.Duration `json:"min_duration"`
}

// RouteMetrics 路由指标
type RouteMetrics struct {
	// 位置
	Location string `json:"location"`

	// 请求次数
	RequestCount int64 `json:"request_count"`

	// 成功次数
	SuccessCount int64 `json:"success_count"`

	// 失败次数
	ErrorCount int64 `json:"error_count"`

	// 平均响应时间
	AvgResponseTime time.Duration `json:"avg_response_time"`

	// 可用设备数
	AvailableDevices int64 `json:"available_devices"`
}

// MetricsCollector 指标收集器接口
type MetricsCollector interface {
	// 记录事件指标
	RecordEventMetrics(eventType string, duration time.Duration, success bool)

	// 记录路由指标
	RecordRouteMetrics(location string, duration time.Duration, success bool, deviceCount int64)

	// 获取用户服务指标
	GetUserServiceMetrics() *UserServiceMetrics

	// 获取事件指标
	GetEventMetrics(eventType string) *EventMetrics

	// 获取路由指标
	GetRouteMetrics(location string) *RouteMetrics

	// 重置指标
	ResetMetrics()
}

// InMemoryMetricsCollector 内存指标收集器
type InMemoryMetricsCollector struct {
	userMetrics  *UserServiceMetrics
	eventMetrics map[string]*EventMetrics
	routeMetrics map[string]*RouteMetrics
}

// NewInMemoryMetricsCollector 创建内存指标收集器
func NewInMemoryMetricsCollector() MetricsCollector {
	return &InMemoryMetricsCollector{
		userMetrics: &UserServiceMetrics{
			LastUpdated: time.Now(),
		},
		eventMetrics: make(map[string]*EventMetrics),
		routeMetrics: make(map[string]*RouteMetrics),
	}
}

// RecordEventMetrics 记录事件指标
func (c *InMemoryMetricsCollector) RecordEventMetrics(eventType string, duration time.Duration, success bool) {
	// 更新用户服务指标
	if eventType == "sdkConnect" {
		c.userMetrics.ConnectEvents++
	} else if eventType == "sdkDisconnect" {
		c.userMetrics.DisconnectEvents++
	}

	if !success {
		c.userMetrics.ErrorCount++
	}

	c.userMetrics.LastUpdated = time.Now()

	// 更新事件指标
	if _, exists := c.eventMetrics[eventType]; !exists {
		c.eventMetrics[eventType] = &EventMetrics{
			EventType:   eventType,
			MinDuration: duration,
			MaxDuration: duration,
		}
	}

	metrics := c.eventMetrics[eventType]
	metrics.Count++

	if success {
		metrics.SuccessCount++
	} else {
		metrics.ErrorCount++
	}

	// 更新平均时间
	if metrics.Count == 1 {
		metrics.AvgDuration = duration
	} else {
		metrics.AvgDuration = (metrics.AvgDuration*time.Duration(metrics.Count-1) + duration) / time.Duration(metrics.Count)
	}

	// 更新最大最小时间
	if duration > metrics.MaxDuration {
		metrics.MaxDuration = duration
	}
	if duration < metrics.MinDuration {
		metrics.MinDuration = duration
	}
}

// RecordRouteMetrics 记录路由指标
func (c *InMemoryMetricsCollector) RecordRouteMetrics(location string, duration time.Duration, success bool, deviceCount int64) {
	c.userMetrics.RouteRequests++
	if !success {
		c.userMetrics.ErrorCount++
	}
	c.userMetrics.LastUpdated = time.Now()

	// 更新路由指标
	if _, exists := c.routeMetrics[location]; !exists {
		c.routeMetrics[location] = &RouteMetrics{
			Location: location,
		}
	}

	metrics := c.routeMetrics[location]
	metrics.RequestCount++

	if success {
		metrics.SuccessCount++
	} else {
		metrics.ErrorCount++
	}

	// 更新平均响应时间
	if metrics.RequestCount == 1 {
		metrics.AvgResponseTime = duration
	} else {
		metrics.AvgResponseTime = (metrics.AvgResponseTime*time.Duration(metrics.RequestCount-1) + duration) / time.Duration(metrics.RequestCount)
	}

	metrics.AvailableDevices = deviceCount
}

// GetUserServiceMetrics 获取用户服务指标
func (c *InMemoryMetricsCollector) GetUserServiceMetrics() *UserServiceMetrics {
	return c.userMetrics
}

// GetEventMetrics 获取事件指标
func (c *InMemoryMetricsCollector) GetEventMetrics(eventType string) *EventMetrics {
	return c.eventMetrics[eventType]
}

// GetRouteMetrics 获取路由指标
func (c *InMemoryMetricsCollector) GetRouteMetrics(location string) *RouteMetrics {
	return c.routeMetrics[location]
}

// ResetMetrics 重置指标
func (c *InMemoryMetricsCollector) ResetMetrics() {
	c.userMetrics = &UserServiceMetrics{
		LastUpdated: time.Now(),
	}
	c.eventMetrics = make(map[string]*EventMetrics)
	c.routeMetrics = make(map[string]*RouteMetrics)
}
