package server

import (
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
	"go.opentelemetry.io/otel/metric"

	"sh_proxy/pkg/utils"
)

// ProviderSet is server providers.
var ProviderSet = wire.NewSet(NewGRPCServer, NewHTTPServer)

var (
	metricRequests metric.Int64Counter
	metricSeconds  metric.Float64Histogram
)

const ServiceName = "user.service"

func init() {
	var err error
	metricRequests, metricSeconds, err = utils.Metrics(ServiceName)
	if err != nil {
		log.Fatalf("utils.Metrics fail: %v", err)
	}
}
