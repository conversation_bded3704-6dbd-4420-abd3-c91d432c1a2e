package server

import (
	"context"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/transport"

	v1 "sh_proxy/api/user/v1"
)

// UserServiceMiddleware 用户服务专用中间件
func UserServiceMiddleware(logger log.Logger) middleware.Middleware {
	return middleware.Chain(
		UserEventLogMiddleware(logger),
		UserRouteLogMiddleware(logger),
		UserMetricsMiddleware(logger),
	)
}

// UserEventLogMiddleware 用户事件日志中间件
func UserEventLogMiddleware(logger log.Logger) middleware.Middleware {
	log := log.NewHelper(log.With(logger, "module", "middleware/user_event"))

	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			// 只处理Event请求
			if eventReq, ok := req.(*v1.EventRequest); ok {
				startTime := time.Now()

				// 从上下文中获取trace_id和request_id
				traceID := getTraceIDFromContext(ctx)
				requestID := getRequestIDFromContext(ctx)

				// 记录事件开始
				log.Infow(
					"msg", "user event started",
					"trace_id", traceID,
					"request_id", requestID,
					"event_type", eventReq.Name,
					"sdk_id", eventReq.SdkId,
					"sdk_ip", eventReq.SdkIp,
					"conn_id", eventReq.ConnId,
					"host", eventReq.Host,
					"supplier", eventReq.Supplier,
					"start_time", startTime.Format(time.RFC3339),
				)

				reply, err := handler(ctx, req)

				duration := time.Since(startTime)

				if err != nil {
					log.Errorw(
						"msg", "user event failed",
						"trace_id", traceID,
						"request_id", requestID,
						"event_type", eventReq.Name,
						"sdk_id", eventReq.SdkId,
						"sdk_ip", eventReq.SdkIp,
						"conn_id", eventReq.ConnId,
						"error", err.Error(),
						"duration_ms", duration.Milliseconds(),
					)
				} else {
					if eventReply, ok := reply.(*v1.EventReply); ok {
						log.Infow(
							"msg", "user event completed",
							"trace_id", traceID,
							"request_id", requestID,
							"event_type", eventReq.Name,
							"sdk_id", eventReq.SdkId,
							"sdk_ip", eventReq.SdkIp,
							"conn_id", eventReq.ConnId,
							"response_message", eventReply.Message,
							"duration_ms", duration.Milliseconds(),
						)
					}
				}

				return reply, err
			}

			return handler(ctx, req)
		}
	}
}

// UserRouteLogMiddleware 用户路由日志中间件
func UserRouteLogMiddleware(logger log.Logger) middleware.Middleware {
	log := log.NewHelper(log.With(logger, "module", "middleware/user_route"))

	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			// 只处理Route请求
			if routeReq, ok := req.(*v1.RouteRequest); ok {
				startTime := time.Now()

				// 从上下文中获取trace_id和request_id
				traceID := getTraceIDFromContext(ctx)
				requestID := getRequestIDFromContext(ctx)

				// 记录路由请求开始
				log.Infow(
					"msg", "user route started",
					"trace_id", traceID,
					"request_id", requestID,
					"location", routeReq.Location,
					"auth_user", maskSensitiveData(routeReq.AuthUser),
					"session", maskSensitiveData(routeReq.Session),
					"target_host", routeReq.Host,
					"target_port", routeReq.Port,
					"start_time", startTime.Format(time.RFC3339),
				)

				reply, err := handler(ctx, req)

				duration := time.Since(startTime)

				if err != nil {
					log.Errorw(
						"msg", "user route failed",
						"trace_id", traceID,
						"request_id", requestID,
						"location", routeReq.Location,
						"auth_user", maskSensitiveData(routeReq.AuthUser),
						"session", maskSensitiveData(routeReq.Session),
						"target_host", routeReq.Host,
						"target_port", routeReq.Port,
						"error", err.Error(),
						"duration_ms", duration.Milliseconds(),
					)
				} else {
					if routeReply, ok := reply.(*v1.RouteReply); ok {
						log.Infow(
							"msg", "user route completed",
							"trace_id", traceID,
							"request_id", requestID,
							"location", routeReq.Location,
							"auth_user", maskSensitiveData(routeReq.AuthUser),
							"session", maskSensitiveData(routeReq.Session),
							"target_host", routeReq.Host,
							"target_port", routeReq.Port,
							"selected_host", routeReply.Host,
							"selected_sdk_id", routeReply.SkdId,
							"selected_sdk_ip", routeReply.SdkIp,
							"duration_ms", duration.Milliseconds(),
						)
					}
				}

				return reply, err
			}

			return handler(ctx, req)
		}
	}
}

// UserMetricsMiddleware 用户服务指标中间件
func UserMetricsMiddleware(logger log.Logger) middleware.Middleware {
	log := log.NewHelper(log.With(logger, "module", "middleware/user_metrics"))

	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			var operation string
			var requestType string

			// 识别请求类型
			switch req.(type) {
			case *v1.EventRequest:
				operation = "Event"
				if eventReq := req.(*v1.EventRequest); eventReq != nil {
					requestType = eventReq.Name
				}
			case *v1.RouteRequest:
				operation = "Route"
				requestType = "route_request"
			default:
				if info, ok := transport.FromServerContext(ctx); ok {
					operation = info.Operation()
				}
				requestType = "unknown"
			}

			startTime := time.Now()
			reply, err := handler(ctx, req)
			duration := time.Since(startTime)

			// 记录指标
			status := "success"
			if err != nil {
				status = "error"
			}

			log.Infow(
				"msg", "user service metrics",
				"operation", operation,
				"request_type", requestType,
				"status", status,
				"duration_ms", duration.Milliseconds(),
				"timestamp", startTime.Unix(),
			)

			// todo 这里可以发送指标到监控系统

			return reply, err
		}
	}
}

// SecurityMiddleware 安全中间件
func SecurityMiddleware(logger log.Logger) middleware.Middleware {
	log := log.NewHelper(log.With(logger, "module", "middleware/security"))

	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			// 安全检查
			if err := performSecurityChecks(ctx, req, log); err != nil {
				log.Warnw(
					"msg", "security check failed",
					"error", err.Error(),
				)
				return nil, err
			}

			return handler(ctx, req)
		}
	}
}

// performSecurityChecks 执行安全检查
func performSecurityChecks(ctx context.Context, req interface{}, log *log.Helper) error {
	// 这里可以实现各种安全检查
	// 例如：
	// 1. IP白名单检查
	// 2. 请求频率限制
	// 3. 参数验证
	// 4. 认证检查

	// 示例：检查Event请求的参数
	if eventReq, ok := req.(*v1.EventRequest); ok {
		if eventReq.SdkId == "" {
			log.Warnw("msg", "invalid event request", "reason", "empty sdk_id")
			// 这里可以返回错误或记录安全事件
		}

		if eventReq.SdkIp == "" {
			log.Warnw("msg", "invalid event request", "reason", "empty sdk_ip")
		}
	}

	// 示例：检查Route请求的参数
	if routeReq, ok := req.(*v1.RouteRequest); ok {
		if routeReq.Host == "" {
			log.Warnw("msg", "invalid route request", "reason", "empty host")
		}
	}

	return nil
}

// getTraceIDFromContext 从上下文中获取trace_id
func getTraceIDFromContext(ctx context.Context) string {
	if traceID, ok := ctx.Value("trace_id").(string); ok {
		return traceID
	}
	return ""
}

// getRequestIDFromContext 从上下文中获取request_id
func getRequestIDFromContext(ctx context.Context) string {
	if requestID, ok := ctx.Value("request_id").(string); ok {
		return requestID
	}
	return ""
}

// maskSensitiveData 掩码敏感数据
func maskSensitiveData(data string) string {
	if data == "" {
		return ""
	}

	if len(data) <= 4 {
		return strings.Repeat("*", len(data))
	}

	// 显示前2位和后2位，中间用*代替
	return data[:2] + strings.Repeat("*", len(data)-4) + data[len(data)-2:]
}
