package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"

	"sh_proxy/app/user/internal/data"
	contextlog "sh_proxy/pkg/log"
	"sh_proxy/pkg/types"
)

// EventPublisher 事件发布器接口
type EventPublisher interface {
	PublishUserEvent(ctx context.Context, event *types.UserEvent) error
}

// MQEventPublisher MQ事件发布器
type MQEventPublisher struct {
	log  *contextlog.Helper
	data *data.Data
}

// MQClient MQ客户端接口
type MQClient interface {
	PublishMessage(exchange, routingKey string, body []byte) error
}

func NewMQEventPublisher(logger log.Logger, data *data.Data) EventPublisher {
	return &MQEventPublisher{
		log:  contextlog.NewHelper(log.With(logger, "module", "service/event_publisher")),
		data: data,
	}
}

func (p *MQEventPublisher) PublishUserEvent(ctx context.Context, event *types.UserEvent) error {
	// 从 context 中获取 trace_id
	traceId := getTraceIDFromContext(ctx)
	if traceId == "" {
		traceId = generateTraceID()
	}

	// 创建 MQMessage
	mqMsg := types.NewUserEventMQMessage(traceId, event)

	// 序列化消息
	body := mqMsg.Marshal()

	// 发布到 MQ（使用现有的队列）
	err := p.data.PublishMsg(ctx, body)
	if err != nil {
		p.log.WithContext(ctx).Errorw("msg", "发布用户事件失败",
			"event_type", event.Name,
			"sdk_id", event.SdkId,
			"trace_id", traceId,
			"error", err,
		)
		return err
	}

	p.log.WithContext(ctx).Infow("msg", "用户事件发布成功",
		"event_type", event.Name,
		"sdk_id", event.SdkId,
		"trace_id", traceId,
	)

	return nil
}

// AsyncEventPublisher 异步事件发布器
type AsyncEventPublisher struct {
	publisher EventPublisher
	log       *log.Helper
	eventChan chan *eventWithContext
	ctx       context.Context
	cancel    context.CancelFunc
}

type eventWithContext struct {
	ctx   context.Context
	event *types.UserEvent
}

func NewAsyncEventPublisher(logger log.Logger, publisher EventPublisher, bufferSize int) *AsyncEventPublisher {
	ctx, cancel := context.WithCancel(context.Background())

	asyncPublisher := &AsyncEventPublisher{
		publisher: publisher,
		log:       log.NewHelper(log.With(logger, "module", "service/async_event_publisher")),
		eventChan: make(chan *eventWithContext, bufferSize),
		ctx:       ctx,
		cancel:    cancel,
	}

	// 启动异步处理 goroutine
	go asyncPublisher.processEvents()

	return asyncPublisher
}

func (p *AsyncEventPublisher) PublishUserEvent(ctx context.Context, event *types.UserEvent) error {
	eventCtx := &eventWithContext{
		ctx:   ctx,
		event: event,
	}

	select {
	case p.eventChan <- eventCtx:
		return nil
	case <-ctx.Done():
		return ctx.Err()
	default:
		p.log.Warnw("msg", "事件队列已满，丢弃事件",
			"event_type", event.Name,
			"sdk_id", event.SdkId,
		)
		return nil // 不返回错误，避免影响主流程
	}
}

func (p *AsyncEventPublisher) processEvents() {
	for {
		select {
		case <-p.ctx.Done():
			p.log.Info("异步事件发布器停止")
			return
		case eventCtx := <-p.eventChan:
			err := p.publisher.PublishUserEvent(eventCtx.ctx, eventCtx.event)
			if err != nil {
				p.log.Errorw("msg", "异步发布事件失败",
					"event_type", eventCtx.event.Name,
					"sdk_id", eventCtx.event.SdkId,
					"error", err,
				)
			}
		}
	}
}

func (p *AsyncEventPublisher) Stop() {
	p.cancel()
	close(p.eventChan)
}

func generateTraceID() string {
	// 简单的 trace ID 生成
	return fmt.Sprintf("trace_%d", time.Now().UnixNano())
}

// MockMQClient 模拟MQ客户端（用于测试）
type MockMQClient struct {
	log *log.Helper
}

func NewMockMQClient(logger log.Logger) MQClient {
	return &MockMQClient{
		log: log.NewHelper(log.With(logger, "module", "mock_mq_client")),
	}
}

func (m *MockMQClient) PublishMessage(exchange, routingKey string, body []byte) error {
	var mqMsg types.MQMessage
	if err := json.Unmarshal(body, &mqMsg); err != nil {
		return err
	}

	m.log.Infow("msg", "模拟发布消息",
		"exchange", exchange,
		"routing_key", routingKey,
		"type", mqMsg.Type,
		"trace_id", mqMsg.TraceId,
	)

	return nil
}
