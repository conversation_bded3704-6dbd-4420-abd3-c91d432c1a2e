package service

import (
	"context"
	"fmt"
	"time"

	"github.com/go-kratos/kratos/v2/log"

	v1 "sh_proxy/api/user/v1"
	"sh_proxy/app/user/internal/biz"
	contextlog "sh_proxy/app/user/internal/pkg/log"
	"sh_proxy/pkg/types"
)

type UserService struct {
	v1.UnimplementedUserServer

	log            *contextlog.Helper
	uc             *biz.UserUsecase
	eventPublisher EventPublisher
}

func NewUserService(logger log.Logger, uc *biz.UserUsecase, eventPublisher EventPublisher) *UserService {
	return &UserService{
		log:            contextlog.NewHelper(log.With(logger, "module", "service/user")),
		uc:             uc,
		eventPublisher: eventPublisher,
	}
}

func (s *UserService) Health(_ context.Context, _ *v1.Empty) (*v1.Empty, error) {
	return &v1.Empty{}, nil
}

func (s *UserService) Event(ctx context.Context, req *v1.EventRequest) (*v1.EventReply, error) {
	// 调用原有的事件处理逻辑
	// reply, err := s.uc.Event(ctx, req)
	if req.Supplier == "" {
		return nil, fmt.Errorf("supplier is empty")
	}
	if req.SdkId == "" {
		return nil, fmt.Errorf("sdk_id is empty")
	}
	if req.SdkIp == "" {
		return nil, fmt.Errorf("sdk_ip is empty")
	}
	if req.Name != types.UserEventConnect && req.Name != types.UserEventDisconnect {
		return nil, fmt.Errorf("event name is invalid")
	}

	// 如果处理成功，异步发布事件到 MQ
	// if err == nil && s.eventPublisher != nil {
	// 异步发布事件（不影响主流程）
	s.publishEventToMQ(ctx, req)
	// }

	return &v1.EventReply{Message: "event received"}, nil
}

// publishEventToMQ 发布事件到MQ
func (s *UserService) publishEventToMQ(ctx context.Context, req *v1.EventRequest) {
	// 从 context 中获取 trace_id
	traceID := getTraceIDFromContext(ctx)

	// 创建用户事件
	userEvent := &types.UserEvent{
		Name:      req.Name,
		SdkId:     req.SdkId,
		SdkIp:     req.SdkIp,
		Host:      req.Host,
		Supplier:  req.Supplier,
		ConnId:    req.ConnId,
		TimeStamp: time.Now().UnixMilli(),
		Meta:      req.Meta,
	}

	// 发布事件
	if err := s.eventPublisher.PublishUserEvent(ctx, userEvent); err != nil {
		s.log.WithContext(ctx).Errorw("msg", "发布用户事件到MQ失败",
			"event_type", req.Name,
			"sdk_id", req.SdkId,
			"trace_id", traceID,
			"error", err,
		)
	} else {
		s.log.WithContext(ctx).Infow("msg", "用户事件已发布到MQ",
			"event_type", req.Name,
			"sdk_id", req.SdkId,
			"trace_id", traceID,
		)
	}
}

func (s *UserService) Route(ctx context.Context, req *v1.RouteRequest) (*v1.RouteReply, error) {
	return s.uc.Route(ctx, req)
}

// 辅助函数
func getTraceIDFromContext(ctx context.Context) string {
	if traceID, ok := ctx.Value("trace_id").(string); ok {
		return traceID
	}
	if traceID, ok := ctx.Value("traceId").(string); ok {
		return traceID
	}
	return ""
}
