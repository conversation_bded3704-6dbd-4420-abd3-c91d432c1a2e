# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: ""
    version: 0.0.1
paths:
    /health:
        get:
            tags:
                - User
            description: Sends a greeting
            operationId: User_Health
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user.v1.Empty'
    /v1/event:
        post:
            tags:
                - User
            operationId: User_Event
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user.v1.EventRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user.v1.EventReply'
    /v1/route:
        post:
            tags:
                - User
            operationId: User_Route
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user.v1.RouteRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user.v1.RouteReply'
    /v1/route-by-ip:
        post:
            tags:
                - User
            operationId: User_RouteByIp
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/user.v1.RouteByIpRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/user.v1.RouteByIpReply'
components:
    schemas:
        consumer.v1.Empty:
            type: object
            properties: {}
        user.v1.Empty:
            type: object
            properties: {}
        user.v1.EventReply:
            type: object
            properties:
                message:
                    type: string
        user.v1.EventRequest:
            type: object
            properties:
                name:
                    type: string
                sdkId:
                    type: string
                sdkIp:
                    type: string
                host:
                    type: string
                supplier:
                    type: string
                meta:
                    type: string
                connId:
                    type: string
        user.v1.IpList:
            type: object
            properties:
                host:
                    type: string
                skdId:
                    type: string
                sdkIp:
                    type: string
                connId:
                    type: string
        user.v1.RouteByIpReply:
            type: object
            properties:
                list:
                    type: array
                    items:
                        $ref: '#/components/schemas/user.v1.IpList'
        user.v1.RouteByIpRequest:
            type: object
            properties:
                supplier:
                    type: string
                ip:
                    type: string
                skdId:
                    type: string
        user.v1.RouteReply:
            type: object
            properties:
                host:
                    type: string
                skdId:
                    type: string
                sdkIp:
                    type: string
                connId:
                    type: string
        user.v1.RouteRequest:
            type: object
            properties:
                location:
                    type: string
                authUser:
                    type: string
                session:
                    type: string
                host:
                    type: string
                port:
                    type: string
                supplier:
                    type: string
tags:
    - name: Consumer
    - name: User
      description: The greeting service definition.
