package bootstrap

import (
	"os"
	"path/filepath"
	"strings"
	"time"

	zapLogger "github.com/go-kratos/kratos/contrib/log/zap/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

type LoggerType string

const (
	LoggerTypeStd LoggerType = "std"
	LoggerTypeZap LoggerType = "zap"
)

// LoggerConf 日志配置
type (
	ZapConfig struct {
		Filename     string // 基础文件名，如果按级别分目录，将用作基础路径
		Level        string
		MaxSize      int32
		MaxAge       int32
		MaxBackups   int32
		SplitByLevel bool   // 是否按日志级别分别存储
		LogDir       string // 日志根目录，如果为空，将使用Filename所在目录
	}
	LoggerConf struct {
		Type LoggerType
		Zap  *ZapConfig
	}
)

// NewLoggerProvider 创建一个新的日志记录器提供者
func NewLoggerProvider(cfg *LoggerConf, serviceInfo *ServiceInfo) log.Logger {
	l := NewLogger(cfg)

	return log.With(
		l,
		"caller", log.DefaultCaller,
		"ts", log.Timestamp("2006-01-02T15:04:05.000Z0700"),
		"service.id", serviceInfo.Id,
		"service.name", serviceInfo.Name,
		"service.version", serviceInfo.Version,
		"trace_id", tracing.TraceID(),
		"span_id", tracing.SpanID(),
	)
}

// NewLogger 创建一个新的日志记录器
func NewLogger(cfg *LoggerConf) log.Logger {
	if cfg == nil {
		return NewStdLogger()
	}

	switch LoggerType(cfg.Type) {
	default:
		fallthrough
	case LoggerTypeStd:
		return NewStdLogger()
	case LoggerTypeZap:
		return NewZapLogger(cfg)
	}
}

// NewStdLogger 创建一个新的日志记录器 - Kratos内置，控制台输出
func NewStdLogger() log.Logger {
	l := log.NewStdLogger(os.Stdout)
	return l
}

// NewZapLogger 创建一个新的日志记录器 - Zap
func NewZapLogger(cfg *LoggerConf) log.Logger {
	encoderConfig := zap.NewProductionEncoderConfig()
	encoderConfig.EncodeTime = EpochTimeEncoder
	encoderConfig.TimeKey = "time"
	encoderConfig.EncodeLevel = zapcore.CapitalLevelEncoder
	encoderConfig.EncodeDuration = zapcore.SecondsDurationEncoder
	encoderConfig.EncodeCaller = zapcore.ShortCallerEncoder
	jsonEncoder := zapcore.NewJSONEncoder(encoderConfig)

	// 解析日志级别
	var minLvl = new(zapcore.Level)
	if err := minLvl.UnmarshalText([]byte(cfg.Zap.Level)); err != nil {
		return nil
	}

	// 准备WriteSyncer
	var cores []zapcore.Core

	// 将输出到控制台
	cores = append(cores, zapcore.NewCore(
		zapcore.NewConsoleEncoder(encoderConfig),
		zapcore.AddSync(os.Stdout),
		minLvl,
	))

	if cfg.Zap.SplitByLevel {
		// 按级别分别存储日志

		// 确定日志基础目录
		baseDir := cfg.Zap.LogDir
		if baseDir == "" {
			if cfg.Zap.Filename != "" {
				baseDir = filepath.Dir(cfg.Zap.Filename)
			} else {
				baseDir = "./logs"
			}
		}

		// 确保日志目录存在
		_ = os.MkdirAll(baseDir, 0755)

		// // 获取文件名（不含路径）
		// baseFilename := filepath.Base(cfg.Zap.Filename)
		// if baseFilename == "." || baseFilename == "" {
		// 	baseFilename = "app.log"
		// }
		//
		// // 不同级别的日志分别存储
		// levelDirs := map[zapcore.Level]string{
		// 	zapcore.DebugLevel: filepath.Join(baseDir, "debug"),
		// 	zapcore.InfoLevel:  filepath.Join(baseDir, "info"),
		// 	zapcore.WarnLevel:  filepath.Join(baseDir, "warn"),
		// 	zapcore.ErrorLevel: filepath.Join(baseDir, "error"),
		// 	zapcore.FatalLevel: filepath.Join(baseDir, "fatal"),
		// }
		//
		// lteInfoDir := filepath.Join(baseDir, "info")
		// gteWarnDir := filepath.Join(baseDir, "warn")
		//
		// // 为每个级别创建目录和writer
		// for level, dir := range levelDirs {
		// 	// 如果级别低于最小日志级别，则跳过
		// 	if level < *minLvl {
		// 		continue
		// 	}
		//
		// 	// 创建目录
		// 	_ = os.MkdirAll(dir, 0755)
		//
		// 	// 创建对应级别的LumberJack logger
		// 	levelLogger := &lumberjack.Logger{
		// 		Filename:   filepath.Join(dir, baseFilename),
		// 		MaxSize:    int(cfg.Zap.MaxSize),
		// 		MaxBackups: int(cfg.Zap.MaxBackups),
		// 		MaxAge:     int(cfg.Zap.MaxAge),
		// 	}
		//
		// 	// 创建levelEnabler，仅对特定级别启用
		// 	levelEnabler := zap.LevelEnablerFunc(func(lvl zapcore.Level) bool {
		// 		return lvl == level
		// 	})

		// 创建core并添加到cores列表
		cores = append(cores, generateLoggerCore(cfg, jsonEncoder, minLvl, baseDir, "info"), generateLoggerCore(cfg, jsonEncoder, minLvl, baseDir, "warn"))
		// }
	} else {
		// 传统方式：所有级别的日志都写入同一个文件
		lumberJackLogger := &lumberjack.Logger{
			Filename:   cfg.Zap.Filename,
			MaxSize:    int(cfg.Zap.MaxSize),
			MaxBackups: int(cfg.Zap.MaxBackups),
			MaxAge:     int(cfg.Zap.MaxAge),
		}
		writeSyncer := zapcore.AddSync(lumberJackLogger)

		cores = append(cores, zapcore.NewCore(
			jsonEncoder,
			writeSyncer,
			minLvl,
		))
	}

	// 合并所有cores
	core := zapcore.NewTee(cores...)
	logger := zap.New(core, zap.AddCallerSkip(3))

	wrapped := zapLogger.NewLogger(logger)
	return wrapped
}

func generateLoggerCore(cfg *LoggerConf, jsonEncoder zapcore.Encoder, minLvl *zapcore.Level, baseDir, levelDir string) zapcore.Core {
	// 获取文件名（不含路径）
	baseFilename := filepath.Base(cfg.Zap.Filename)
	if baseFilename == "." || baseFilename == "" {
		baseFilename = "app.log"
	}
	dir := filepath.Join(baseDir, levelDir)
	ext := filepath.Ext(baseFilename)
	filename := filepath.Join(dir, strings.TrimSuffix(baseFilename, ext)+"_"+levelDir+ext)

	// 创建目录
	_ = os.MkdirAll(dir, 0755)

	// 创建对应级别的LumberJack logger
	levelLogger := &lumberjack.Logger{
		Filename:   filename,
		MaxSize:    int(cfg.Zap.MaxSize),
		MaxBackups: int(cfg.Zap.MaxBackups),
		MaxAge:     int(cfg.Zap.MaxAge),
	}

	// 创建levelEnabler，仅对特定级别启用
	levelEnabler := zap.LevelEnablerFunc(func(lvl zapcore.Level) bool {
		if lvl < *minLvl {
			return false
		}
		switch levelDir {
		case "info":
			return lvl <= zapcore.InfoLevel
		case "warn":
			return lvl >= zapcore.WarnLevel
		default:
			return lvl >= zapcore.DebugLevel
		}
	})

	return zapcore.NewCore(
		jsonEncoder,
		zapcore.AddSync(levelLogger),
		levelEnabler,
	)
}

func EpochMillisTimeEncoder(t time.Time, enc zapcore.PrimitiveArrayEncoder) {
	enc.AppendInt64(t.UnixMilli())
}

func EpochTimeEncoder(t time.Time, enc zapcore.PrimitiveArrayEncoder) {
	enc.AppendInt64(t.Unix())
}
