package bootstrap

import (
	"github.com/go-kratos/kratos/v2/log"
	"github.com/redis/go-redis/v9"
	"time"
)

type RedisCfg struct {
	Addr         string
	Password     string
	Db           int
	DialTimeout  time.Duration
	ReadTimeout  time.Duration
	WriteTimeout time.Duration
}

// NewRedisClient 创建Redis客户端
func NewRedisClient(cfg *RedisCfg, logger *log.Helper) *redis.Client {
	rdb := redis.NewClient(&redis.Options{
		Addr:         cfg.Addr,
		Password:     cfg.Password,
		DB:           int(cfg.Db),
		DialTimeout:  cfg.DialTimeout,
		WriteTimeout: cfg.WriteTimeout,
		ReadTimeout:  cfg.ReadTimeout,
	})
	if rdb == nil {
		logger.Fatalf("failed opening connection to redis")
		return nil
	}
	//rdb.AddHook(redisotel.NewTracingHook())

	return rdb
}
