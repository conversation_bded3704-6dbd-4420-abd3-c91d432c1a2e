package log

import (
	"context"
	"time"

	"github.com/go-kratos/kratos/v2/log"
)

// AdvancedLogger 高级日志器，支持更多功能
type AdvancedLogger struct {
	logger log.Logger
	helper *log.Helper
	fields []any
}

// NewAdvancedLogger 创建高级日志器
func NewAdvancedLogger(logger log.Logger, keyvals ...any) *AdvancedLogger {
	return &AdvancedLogger{
		logger: logger,
		helper: log.NewHelper(log.With(logger, keyvals...)),
		fields: keyvals,
	}
}

// WithContext 创建带有 context 的日志器
func (l *AdvancedLogger) WithContext(ctx context.Context) *AdvancedContextLogger {
	return &AdvancedContextLogger{
		ctx:    ctx,
		logger: l.logger,
		helper: l.helper,
		fields: l.fields,
	}
}

// WithFields 添加字段到日志器
func (l *AdvancedLogger) WithFields(keyvals ...any) *AdvancedLogger {
	newFields := append(l.fields, keyvals...)
	return &AdvancedLogger{
		logger: l.logger,
		helper: log.<PERSON>elper(log.With(l.logger, newFields...)),
		fields: newFields,
	}
}

// AdvancedContextLogger 带有 context 的高级日志器
type AdvancedContextLogger struct {
	ctx    context.Context
	logger log.Logger
	helper *log.Helper
	fields []any
}

// extractAllFields 提取所有字段（context + 预设字段 + 动态字段）
func (l *AdvancedContextLogger) extractAllFields(keyvals ...any) []any {
	var allFields []any

	// 1. 添加 context 字段
	contextFields := l.extractContextFields()
	allFields = append(allFields, contextFields...)

	// 2. 添加预设字段
	allFields = append(allFields, l.fields...)

	// 3. 添加动态字段
	allFields = append(allFields, keyvals...)

	return allFields
}

// extractContextFields 从 context 中提取字段
func (l *AdvancedContextLogger) extractContextFields() []any {
	var fields []any

	// 提取 trace_id
	if traceID, ok := l.ctx.Value("trace_id").(string); ok && traceID != "" {
		fields = append(fields, "trace_id", traceID)
	}

	// 提取 request_id
	if requestID, ok := l.ctx.Value("request_id").(string); ok && requestID != "" {
		fields = append(fields, "request_id", requestID)
	}

	// 提取 user_id
	if userID, ok := l.ctx.Value("user_id").(string); ok && userID != "" {
		fields = append(fields, "user_id", userID)
	}

	// 提取 session_id
	if sessionID, ok := l.ctx.Value("session_id").(string); ok && sessionID != "" {
		fields = append(fields, "session_id", sessionID)
	}

	// 提取 span_id（用于分布式追踪）
	if spanID, ok := l.ctx.Value("span_id").(string); ok && spanID != "" {
		fields = append(fields, "span_id", spanID)
	}

	// 提取 correlation_id（用于关联多个请求）
	if correlationID, ok := l.ctx.Value("correlation_id").(string); ok && correlationID != "" {
		fields = append(fields, "correlation_id", correlationID)
	}

	return fields
}

// WithFields 添加字段到当前日志器
func (l *AdvancedContextLogger) WithFields(keyvals ...any) *AdvancedContextLogger {
	newFields := append(l.fields, keyvals...)
	return &AdvancedContextLogger{
		ctx:    l.ctx,
		logger: l.logger,
		helper: l.helper,
		fields: newFields,
	}
}

// WithError 添加错误字段
func (l *AdvancedContextLogger) WithError(err error) *AdvancedContextLogger {
	if err != nil {
		return l.WithFields("error", err.Error())
	}
	return l
}

// WithDuration 添加耗时字段
func (l *AdvancedContextLogger) WithDuration(duration time.Duration) *AdvancedContextLogger {
	return l.WithFields(
		"duration", duration.String(),
		"duration_ms", duration.Milliseconds(),
	)
}

// WithTimestamp 添加时间戳字段
func (l *AdvancedContextLogger) WithTimestamp() *AdvancedContextLogger {
	return l.WithFields("timestamp", time.Now().Unix())
}

// Debug 记录 Debug 级别日志
func (l *AdvancedContextLogger) Debug(a ...any) {
	l.helper.Debug(a...)
}

// Debugf 记录格式化的 Debug 级别日志
func (l *AdvancedContextLogger) Debugf(format string, a ...any) {
	l.helper.Debugf(format, a...)
}

// Debugw 记录带字段的 Debug 级别日志
func (l *AdvancedContextLogger) Debugw(keyvals ...any) {
	allFields := l.extractAllFields(keyvals...)
	l.helper.Debugw(allFields...)
}

// Info 记录 Info 级别日志
func (l *AdvancedContextLogger) Info(a ...any) {
	l.helper.Info(a...)
}

// Infof 记录格式化的 Info 级别日志
func (l *AdvancedContextLogger) Infof(format string, a ...any) {
	l.helper.Infof(format, a...)
}

// Infow 记录带字段的 Info 级别日志
func (l *AdvancedContextLogger) Infow(keyvals ...any) {
	allFields := l.extractAllFields(keyvals...)
	l.helper.Infow(allFields...)
}

// Warn 记录 Warn 级别日志
func (l *AdvancedContextLogger) Warn(a ...any) {
	l.helper.Warn(a...)
}

// Warnf 记录格式化的 Warn 级别日志
func (l *AdvancedContextLogger) Warnf(format string, a ...any) {
	l.helper.Warnf(format, a...)
}

// Warnw 记录带字段的 Warn 级别日志
func (l *AdvancedContextLogger) Warnw(keyvals ...any) {
	allFields := l.extractAllFields(keyvals...)
	l.helper.Warnw(allFields...)
}

// Error 记录 Error 级别日志
func (l *AdvancedContextLogger) Error(a ...any) {
	l.helper.Error(a...)
}

// Errorf 记录格式化的 Error 级别日志
func (l *AdvancedContextLogger) Errorf(format string, a ...any) {
	l.helper.Errorf(format, a...)
}

// Errorw 记录带字段的 Error 级别日志
func (l *AdvancedContextLogger) Errorw(keyvals ...any) {
	allFields := l.extractAllFields(keyvals...)
	l.helper.Errorw(allFields...)
}

// Fatal 记录 Fatal 级别日志
func (l *AdvancedContextLogger) Fatal(a ...any) {
	l.helper.Fatal(a...)
}

// Fatalf 记录格式化的 Fatal 级别日志
func (l *AdvancedContextLogger) Fatalf(format string, a ...any) {
	l.helper.Fatalf(format, a...)
}

// Fatalw 记录带字段的 Fatal 级别日志
func (l *AdvancedContextLogger) Fatalw(keyvals ...any) {
	allFields := l.extractAllFields(keyvals...)
	l.helper.Fatalw(allFields...)
}

// 便捷方法

// LogOperation 记录操作日志
func (l *AdvancedContextLogger) LogOperation(operation string, keyvals ...any) {
	fields := append([]any{"operation", operation}, keyvals...)
	l.Infow(fields...)
}

// LogError 记录错误日志
func (l *AdvancedContextLogger) LogError(operation string, err error, keyvals ...any) {
	fields := append([]any{"operation", operation, "error", err.Error()}, keyvals...)
	l.Errorw(fields...)
}

// LogPerformance 记录性能日志
func (l *AdvancedContextLogger) LogPerformance(operation string, duration time.Duration, keyvals ...any) {
	fields := append([]any{
		"operation", operation,
		"duration", duration.String(),
		"duration_ms", duration.Milliseconds(),
	}, keyvals...)

	// 如果耗时超过阈值，使用 Warn 级别
	if duration > 5*time.Second {
		l.Warnw(append([]any{"msg", "slow operation detected"}, fields...)...)
	} else {
		l.Infow(append([]any{"msg", "operation completed"}, fields...)...)
	}
}

// LogBusinessEvent 记录业务事件日志
func (l *AdvancedContextLogger) LogBusinessEvent(event string, keyvals ...any) {
	fields := append([]any{
		"event_type", "business",
		"event_name", event,
		"timestamp", time.Now().Unix(),
	}, keyvals...)
	l.Infow(fields...)
}

// LogSecurityEvent 记录安全事件日志
func (l *AdvancedContextLogger) LogSecurityEvent(event string, keyvals ...any) {
	fields := append([]any{
		"event_type", "security",
		"event_name", event,
		"timestamp", time.Now().Unix(),
	}, keyvals...)
	l.Warnw(fields...)
}

// LogAuditEvent 记录审计事件日志
func (l *AdvancedContextLogger) LogAuditEvent(action string, resource string, keyvals ...any) {
	fields := append([]any{
		"event_type", "audit",
		"action", action,
		"resource", resource,
		"timestamp", time.Now().Unix(),
	}, keyvals...)
	l.Infow(fields...)
}

// 性能监控相关方法

// StartTimer 开始计时
func (l *AdvancedContextLogger) StartTimer(operation string) *Timer {
	return &Timer{
		logger:    l,
		operation: operation,
		startTime: time.Now(),
	}
}

// Timer 计时器
type Timer struct {
	logger    *AdvancedContextLogger
	operation string
	startTime time.Time
}

// End 结束计时并记录日志
func (t *Timer) End(keyvals ...any) {
	duration := time.Since(t.startTime)
	t.logger.LogPerformance(t.operation, duration, keyvals...)
}

// EndWithError 结束计时并记录错误日志
func (t *Timer) EndWithError(err error, keyvals ...any) {
	duration := time.Since(t.startTime)
	fields := append([]any{
		"duration", duration.String(),
		"duration_ms", duration.Milliseconds(),
	}, keyvals...)
	t.logger.LogError(t.operation, err, fields...)
}
