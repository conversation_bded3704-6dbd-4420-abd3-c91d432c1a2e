package types

import (
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"time"
)

// MQMessage 统一的消息队列消息结构
type MQMessage struct {
	Type      string                 `json:"type"`      // 消息类型
	TraceId   string                 `json:"trace_id"`  // 追踪ID
	Message   string                 `json:"message"`   // 消息内容（JSON字符串）
	Timestamp int64                  `json:"timestamp"` // 时间戳
	Retry     int                    `json:"retry"`     // 重试次数
	MaxRetry  int                    `json:"max_retry"` // 最大重试次数
	Extra     map[string]interface{} `json:"extra"`     // 扩展字段
}

// 消息类型常量
const (
	MQMessageTypeUserEvent    = "user_event"   // 用户事件
	MQMessageTypeTaskResult   = "task_result"  // 任务结果
	MQMessageTypeNotification = "notification" // 通知
)

func NewMQMessage(t, trace, msg string) *MQMessage {
	return &MQMessage{
		Type:      t,
		TraceId:   trace,
		Message:   msg,
		Timestamp: time.Now().UnixMilli(),
		Retry:     0,
		MaxRetry:  3,
		Extra:     make(map[string]interface{}),
	}
}

// NewUserEventMQMessage 创建用户事件消息
func NewUserEventMQMessage(traceId string, userEvent *UserEvent) *MQMessage {
	eventData, _ := json.Marshal(userEvent)
	return &MQMessage{
		Type:      MQMessageTypeUserEvent,
		TraceId:   traceId,
		Message:   string(eventData),
		Timestamp: time.Now().UnixMilli(),
		Retry:     0,
		MaxRetry:  0,
		Extra:     make(map[string]interface{}),
	}
}

func (m *MQMessage) Marshal() []byte {
	msgByte, _ := json.Marshal(m)
	return msgByte
}

// UnmarshalUserEvent 解析用户事件
func (m *MQMessage) UnmarshalUserEvent() (*UserEvent, error) {
	if m.Type != MQMessageTypeUserEvent {
		return nil, fmt.Errorf("消息类型不匹配，期望: %s, 实际: %s", MQMessageTypeUserEvent, m.Type)
	}

	var userEvent UserEvent
	err := json.Unmarshal([]byte(m.Message), &userEvent)
	if err != nil {
		return nil, fmt.Errorf("解析用户事件失败: %w", err)
	}

	return &userEvent, nil
}

// ShouldRetry 判断是否应该重试
func (m *MQMessage) ShouldRetry() bool {
	return m.Retry < m.MaxRetry
}

// IncrementRetry 增加重试次数
func (m *MQMessage) IncrementRetry() {
	m.Retry++
}

// generateTraceID 生成Trace ID
func generateTraceID() string {
	// 生成16字节的随机数作为trace_id
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		// 如果随机数生成失败，使用时间戳作为备选方案
		return fmt.Sprintf("trace_%d", time.Now().UnixNano())
	}
	return hex.EncodeToString(bytes)
}
