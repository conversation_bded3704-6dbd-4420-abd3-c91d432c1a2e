package utils

import (
	"github.com/go-kratos/kratos/v2/middleware/metrics"
	"github.com/pkg/errors"
	"go.opentelemetry.io/otel/exporters/prometheus"
	"go.opentelemetry.io/otel/metric"
	sdkmetric "go.opentelemetry.io/otel/sdk/metric"
)

func Metrics(serviceName string) (_metricRequests metric.Int64Counter, _metricSeconds metric.Float64Histogram, err error) {
	exporter, err := prometheus.New()
	if err != nil {
		err = errors.Wrap(err, "prometheus.New fail")
		return
	}
	provider := sdkmetric.NewMeterProvider(sdkmetric.WithReader(exporter))
	meter := provider.Meter(serviceName)

	_metricRequests, err = metrics.DefaultRequestsCounter(meter, metrics.DefaultServerRequestsCounterName)
	if err != nil {
		err = errors.Wrap(err, "DefaultRequestsCounter fail")
		return
	}

	_metricSeconds, err = metrics.DefaultSecondsHistogram(meter, metrics.DefaultServerSecondsHistogramName)
	if err != nil {
		err = errors.Wrap(err, "DefaultSecondsHistogram fail")
		return
	}

	return
}
